# Template frontend CI

image: node:lts

pipelines:
  tags:
    "*-dev":
      - step:
          name: Build and release docker image
          caches:
            - docker
          services:
            - docker
          script:
            - docker login -u "$DOCKERHUB_USERNAME" -p "$DOCKERHUB_PASSWORD"
            - docker build -t iqodev/$DOCKER_IMAGE:$BITBUCKET_TAG .
            - docker push iqodev/$DOCKER_IMAGE:$BITBUCKET_TAG

    "*-rc":
      - step:
          name: Build and release docker image
          deployment: production
          caches:
            - docker
          services:
            - docker
          script:
            - docker login -u "$DOCKERHUB_USERNAME" -p "$DOCKERHUB_PASSWORD"
            - docker build --no-cache -t iqodev/$DOCKER_IMAGE:$BITBUCKET_TAG .
            - docker push iqodev/$DOCKER_IMAGE:$BITBUCKET_TAG
options:
  docker: true
  size: 2x

definitions:
  services:
    docker:
      memory: 4096
