

// export const BASE_URL = "https://digitaltwin.rndlabs.dev/api";
// export const BASE_URL = "https://k5l9zfkv-5656.euw.devtunnels.ms/api";

class AuthService {
    constructor() {
        this.API_URL = `/api/login_admin`;
    }

    async login(email, password) {
        try {

            const response = await fetch(this.API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    Email: email,
                    Password: password
                })
            });

            if (!response.ok) {
                throw new Error('Login failed');
            }

            const data = await response.json();

            localStorage.setItem('isAuthenticated', 'true');
            if (data.access_token) {
                localStorage.setItem('token', data.access_token);
            }

            console.log(localStorage.getItem('token'));

            return data;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    async logout() {
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('token');
        sessionStorage.clear();
        this.currentUser = null;
    }

    isAuthenticated() {
        return localStorage.getItem('isAuthenticated') === 'true';
    }
}

export const authService = new AuthService(); 