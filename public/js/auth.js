import { authService } from '../services/authService.js';

document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const errorMessage = document.getElementById('errorMessage');

    try {
        await authService.login(email, password);
        window.location.href = 'index.html';
    } catch (error) {
        errorMessage.textContent = 'Invalid email or password';
        errorMessage.style.display = 'block';
    }
}); 