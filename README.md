# Iraq Digital Twin

## Overview

Iraq Digital Twin is a 3D JavaScript project aimed at creating a digital twin for various aspects of Iraq. The project visualizes data on a 3D map of Iraq, providing an interactive and scalable platform to simulate and explore real-world scenarios. It leverages modern web development tools and frameworks to offer a seamless experience.

## Prerequisites

Before you begin, make sure you have the following software installed:

- [Node.js](https://nodejs.org/) (v14 or higher)
- [npm](https://www.npmjs.com/) (comes with Node.js)

note: the development face doing on nodejs version of `22.12.0`.

## Installation

1. Clone this repository to your local machine:

```bash
git clone https://bitbucket.org/iqodev/iraq-digital-twin.git
```

2. Navigate to the project directory:

```bash
cd iraq-digital-twin
```

3. Install the required dependencies:

```bash
npm install
```

## Running the Project

To start the project locally, run the following command:

```bash
npm start
```

This will start a development server and open the project in your default web browser. You can now begin developing and testing your project on port `8080`.
