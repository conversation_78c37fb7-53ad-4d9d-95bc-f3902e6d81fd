import { Cartesian3, Color, JulianDate, SampledPositionProperty, Math as CesiumMath, VelocityOrientationProperty } from "cesium";

export function renderISS(viewer) {
    console.log("iss loaded");

    // Define the orbit positions for the ISS
    const orbitPositions = new SampledPositionProperty();

    // Define timestamps and positions (example orbit data)
    const start = JulianDate.fromIso8601("2025-01-16T00:00:00Z");
    // const stop = JulianDate.addSeconds(start, 5400, new JulianDate()); // 10 minutes of orbit
    const stop = JulianDate.addSeconds(start, 2, new JulianDate()); // 10 minutes of orbit

    const timeStepInSeconds = 10;
    for (let i = 0; i <= 600; i += timeStepInSeconds) {
        const time = JulianDate.addSeconds(start, i, new JulianDate());

        // Calculate orbital position (simplified circular orbit)
        const longitude = (i * 0.1) % 360; // Orbit longitude (wrap around Earth)
        const latitude = 10 * Math.sin(CesiumMath.toRadians(i * 0.1)); // Simulated latitude variation
        const altitude = 400000; // 400 km above the Earth's surface

        // Add the position to the orbit
        orbitPositions.addSample(time, Cartesian3.fromDegrees(longitude, latitude, altitude));
    }


    // Add the ISS entity
    const issEntity = viewer.entities.add({
        name: "International Space Station",
        position: orbitPositions,
        model: {
            uri: "/Model/iss.glb",
            minimumPixelSize: 300,
            maximumScale: 20000,
        },
        orientation: new VelocityOrientationProperty(orbitPositions), // Orient the model to face its velocity
        label: {
            text: "ISS",
            fillColor: Color.WHITE,
            outlineColor: Color.BLACK,
            outlineWidth: 2,
            showBackground: true,
            backgroundColor: new Color(0.1, 0.1, 0.1, 0.52),
            // scaleByDistance: new NearFarScalar(5000000, 5, 40000000, 1),
            pixelOffset: new Cartesian3(70, 0, 0), // Offset the label below the model
            // scale: 1.5, // Scale the label for better visibility
        },
    });
}