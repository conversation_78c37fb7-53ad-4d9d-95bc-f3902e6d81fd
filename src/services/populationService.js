import { API_ENDPOINTS } from '../config/api';

const API_URL = API_ENDPOINTS.POPULATION.ALL;

export const fetchPopulationData = async () => {
    try {
        // Get the JWT token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
            throw new Error('No authentication token found');
        }

        const response = await fetch(API_URL, {
            method: 'GET',
            headers: {
                'Authorization': `<PERSON><PERSON> ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Unauthorized: Invalid or expired token');
            }
            throw new Error("Network response was not ok: " + response.statusText);
        }
        return await response.json();
    } catch (error) {
        console.error("Error fetching city data:", error);
        throw error;
    }
};

const normalizePopulation = (value, min, max, minSize = 20, maxSize = 100) => {
    return ((value - min) / (max - min)) * (maxSize - minSize) + minSize;
};

export const preparePopulationData = async () => {
    try {
        const data = await fetchPopulationData();
        const populations = data.map(province => province.population_count);
        const minPopulation = Math.min(...populations);
        const maxPopulation = Math.max(...populations);

        return data.map(province => ({
            longitude: province.longitude,
            latitude: province.latitude,
            name: province.province_name,
            population: province.population_count,
            description: province.province_description,
            pixelSize: normalizePopulation(province.population_count, minPopulation, maxPopulation)
        }));
    } catch (error) {
        console.error("Error preparing population data:", error);
        throw error;
    }
};

export const loadCityData = async () => {
    try {
        const data = await fetchPopulationData();
        const populations = data.map(province => province.population_count);
        const minPopulation = Math.min(...populations);
        const maxPopulation = Math.max(...populations);

        data.forEach((province) => {
            const entity = viewer.entities.add({
                position: Cartesian3.fromDegrees(province.longitude, province.latitude, heightOffset),
                point: {
                    color: Color.fromBytes(207, 109, 73, 205),
                    outlineColor: Color.fromBytes(165, 87, 58, 255),
                    outlineWidth: 2,
                    pixelSize: pixelSize,
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    distanceDisplayCondition: new DistanceDisplayCondition(400000, 2700000)
                },
                properties: {
                    group: "population",  // Make sure this is consistent
                },
                name: "Iraq - " + province.province_name,
                description: `
                    <p>City Name: ${province.province_name}</p>
                    <p>Population: ${province.population_count}</p>
                    <p>Description: ${province.province_description || "No additional data available."}</p>
                `,
            });
            entity.show = true; // Set default visibility to true
        });

        return data;
    } catch (error) {
        console.error("Error loading city data:", error);
        throw error;
    }
}; 