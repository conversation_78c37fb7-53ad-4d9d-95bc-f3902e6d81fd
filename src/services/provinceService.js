import { API_ENDPOINTS } from '../config/api';

export const fetchProvinces = async () => {
    try {
        const token = localStorage.getItem('token');
        if (!token) {
            throw new Error('No authentication token found');
        }

        const response = await fetch(API_ENDPOINTS.PROVINCES.ALL, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Unauthorized: Invalid or expired token');
            }
            throw new Error("Network response was not ok: " + response.statusText);
        }

        return await response.json();
    } catch (error) {
        console.error("Error fetching provinces:", error);
        throw error;
    }
};

export const fetchCitiesByProvince = async (provinceId) => {
    try {
        const token = localStorage.getItem('token');
        if (!token) {
            throw new Error('No authentication token found');
        }

        const response = await fetch(API_ENDPOINTS.CITIES.BY_PROVINCE(provinceId), {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Unauthorized: Invalid or expired token');
            }
            throw new Error("Network response was not ok: " + response.statusText);
        }

        return await response.json();
    } catch (error) {
        console.error("Error fetching cities:", error);
        throw error;
    }
};