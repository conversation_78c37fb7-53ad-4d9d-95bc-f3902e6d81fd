import { Car<PERSON>ian<PERSON>, <PERSON><PERSON><PERSON>, Vert<PERSON><PERSON><PERSON><PERSON>, HeightReference } from "cesium";
import { entityManager } from "../managers/entityManager";
import { PinBuilder, Color, CustomDataSource } from 'cesium';
import { setupBusinessClustering } from "../utils/businessClustering";
import { fetchCategories, findCategoryById } from "./categoryService";
import * as Cesium from 'cesium';

const API_URL = `/api/businesses`;

// Cache for categories to avoid fetching them multiple times
let categoriesCache = null;

// Function to get the categories (either from cache or by fetching)
const getCategoriesCache = async () => {
    if (!categoriesCache) {
        categoriesCache = await fetchCategories();
    }
    return categoriesCache;
};

// Default icon path - this is a local path, not from the backend
const DEFAULT_ICON_PATH = "/Icons/business.svg";

export const fetchBusinessCounts = async (selectedCities = []) => {
    try {
        // Get the JWT token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
            throw new Error('No authentication token found');
        }

        // If no cities are selected, return empty array
        if (selectedCities.length === 0) {
            return [];
        }

        // Build the URL with query parameters
        const url = `${API_URL}/count?cities=${selectedCities.join(',')}`;

        // Print the URL to console for debugging
        console.log("Business count URL:", url);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Unauthorized: Invalid or expired token');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Make sure we return an array of objects with category_id and count properties
        if (Array.isArray(data)) {
            return data;
        } else if (typeof data === 'object') {
            // If the API returns an object with category_id as keys and counts as values
            // Convert it to the expected format
            return Object.entries(data).map(([category_id, count]) => ({
                category_id: parseInt(category_id),
                count
            }));
        }

        return [];
    } catch (error) {
        console.error('Error fetching business count data:', error);
        return [];
    }
};

export const fetchBusinesses = async (categoryId = null, selectedCities = []) => {
    try {
        // Get the JWT token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
            throw new Error('No authentication token found');
        }

        // Build the URL with query parameters
        let url = `${API_URL}?`;
        if (categoryId) {
            url += `categories=${categoryId}`;
        }
        if (selectedCities.length > 0) {
            url += `${categoryId ? '&' : ''}cities=${selectedCities.join(',')}`;
        }

        // Print the URL to console
        console.log("Business filter URL:", url);
        // Also show in a more visible way for debugging
        console.warn("Business filter URL:", url);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Unauthorized: Invalid or expired token');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        return Array.isArray(data) ? data : (data.businesses || []);
    } catch (error) {
        console.error('Error fetching business data:', error);
        throw error;
    }
};

export const displayBusinessPins = async (businesses, viewer, categoryId = null) => {

    console.error(businesses);

    try {
        // Get the JWT token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
            console.warn('No authentication token found, proceeding without authentication');
        }

        // Remove existing business entities for the specific category if provided
        if (categoryId) {
            const entities = viewer.entities.values.filter(entity =>
                entity.properties &&
                entity.properties.getValue().group === 'business' &&
                entity.properties.getValue().categoryId === categoryId
            );
            entities.forEach(entity => viewer.entities.remove(entity));
        } else {
            entityManager.removeByGroup('business', viewer);
        }

        // Create a new data source for the businesses
        const businessDataSource = new CustomDataSource('businesses');
        viewer.dataSources.add(businessDataSource);

        // Initialize PinBuilder for business pins
        const pinBuilder = new PinBuilder();

        // Use the local path for the default icon (not from the backend)
        let defaultPin;
        try {
            console.log(`Loading default icon from local path: ${DEFAULT_ICON_PATH}`);
            // Create a Resource with authorization header if token exists
            const defaultIconResource = new Cesium.Resource({
                url: DEFAULT_ICON_PATH,
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });
            defaultPin = await pinBuilder.fromUrl(defaultIconResource, Color.BLACK, 48);
        } catch (error) {
            console.error('Error loading default business icon:', error);
            // Create a simple default pin as fallback
            defaultPin = pinBuilder.fromColor(Color.ROYALBLUE, 48).toDataURL();
        }

        // Get categories for icon mapping
        const categories = await getCategoriesCache();

        // Cache for pin images to avoid recreating the same pins
        const pinCache = new Map();

        // Add business entities
        for (const business of businesses) {
            if (business.latitude && business.longitude) {
                // Determine which icon to use
                let pinImage = defaultPin;

                // Try to find the specific category for this business
                const businessCategoryId = business.category_id;
                if (businessCategoryId) {
                    // Check if we already have this pin in cache
                    if (pinCache.has(businessCategoryId)) {
                        pinImage = pinCache.get(businessCategoryId);
                    } else {
                        // Find the category for this specific business
                        const category = findCategoryById(categories, businessCategoryId);
                        if (category && category.icon_link) {
                            try {

                                // Create a Resource with authorization header if token exists
                                const iconResource = new Cesium.Resource({
                                    url: category.icon_link,
                                    headers: token ? {
                                        'Authorization': `Bearer ${token}`
                                    } : {}
                                });

                                // Use the category icon if available
                                pinImage = await pinBuilder.fromUrl(iconResource, Color.BLACK, 48);
                                // Cache the pin for future use
                                pinCache.set(businessCategoryId, pinImage);
                            } catch (error) {
                                console.error(`Error loading category icon for business ${business.name}:`, error);
                                // Fallback to default pin if there's an error
                                pinImage = defaultPin;
                                // Cache the default pin to avoid trying again
                                pinCache.set(businessCategoryId, defaultPin);
                            }
                        } else {
                            // Cache the default pin for this category
                            pinCache.set(businessCategoryId, defaultPin);
                        }
                    }
                }

                const entity = new Entity({
                    name: business.name,
                    description: `
                        <div style="font-family: Arial, sans-serif; line-height: 1.5;">
                            <h2 style="font-size: 18px; margin: 0; padding: 0;">${business.name}</h2>
                            <p style="font-size: 14px; margin-top: 10px;">${business.description || 'No description available.'}</p>
                            <ul style="font-size: 14px; padding-left: 20px; margin: 10px 0;">
                                <li>Category: ${business.category_name}</li>
                                <li>City: ${business.city_name}</li>
                                ${business.phone_number ? `<li>Phone: ${business.phone_number}</li>` : ''}
                            </ul>
                            ${business.working_hours && business.working_hours.length > 0 ? `
                                <div style="margin-top: 15px;">
                                    <h3 style="font-size: 16px; margin: 0 0 10px 0;">Working Hours:</h3>
                                    <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                                        ${business.working_hours.map(hours => `
                                            <tr>
                                                <td style="padding: 4px 8px; border-bottom: 1px solid #eee;">${hours.day}</td>
                                                <td style="padding: 4px 8px; border-bottom: 1px solid #eee;">${hours.hours}</td>
                                            </tr>
                                        `).join('')}
                                    </table>
                                </div>
                            ` : ''}
                            ${business.links && business.links.length > 0 ? `
                                <div style="margin-top: 15px;">
                                    ${business.links.length === 1
                                ? `<p style="margin: 0;">Visit our ${business.links[0].type}: <a href="${business.links[0].url}" target="_blank" style="color: #0066cc; text-decoration: none;">Click here</a></p>`
                                : `
                                            <h3 style="font-size: 16px; margin: 0 0 10px 0;">Find us online:</h3>
                                            <ul style="list-style-type: disc; padding-left: 20px; margin: 0;">
                                                ${business.links.map(link => `
                                                    <li style="margin-bottom: 8px;">
                                                        <a href="${link.url}" target="_blank" style="color: #0066cc; text-decoration: none;">
                                                            ${link.type.charAt(0).toUpperCase() + link.type.slice(1)}
                                                        </a>
                                                    </li>
                                                `).join('')}
                                            </ul>
                                        `
                            }
                                </div>
                            ` : ''}
                        </div>
                    `,
                    position: Cartesian3.fromDegrees(
                        business.longitude,
                        business.latitude,
                        0
                    ),
                    billboard: {
                        image: pinImage,
                        verticalOrigin: VerticalOrigin.BOTTOM,
                        heightReference: HeightReference.CLAMP_TO_GROUND,
                    },
                    properties: {
                        group: 'business',
                        categoryId: business.category_id,
                        city_id: business.city_id
                    }
                });
                businessDataSource.entities.add(entity);
            }
        }

        // Setup clustering for the business data source
        setupBusinessClustering(businessDataSource);

        return businessDataSource;
    } catch (error) {
        console.error('Error displaying business pins:', error);
        throw error;
    }
}; 