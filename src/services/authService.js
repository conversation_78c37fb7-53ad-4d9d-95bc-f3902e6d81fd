class AuthService {
    constructor() {
        // For demo purposes, hardcoded credentials
        this.validCredentials = {
            'admin': 'admin123',
            'user': 'user123'
        };
    }

    login(username, password) {
        return new Promise((resolve, reject) => {
            // Simulate API call delay
            setTimeout(() => {
                if (this.validCredentials[username] === password) {
                    localStorage.setItem('isAuthenticated', 'true');
                    resolve(true);
                } else {
                    reject(new Error('Invalid credentials'));
                }
            }, 500);
        });
    }

    logout() {
        localStorage.removeItem('isAuthenticated');
    }

    isAuthenticated() {
        return localStorage.getItem('isAuthenticated') === 'true';
    }
}

export const authService = new AuthService(); 