export const fetchCategories = async () => {
    try {
        // Get the JWT token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
            throw new Error('No authentication token found');
        }

        const response = await fetch(`/api/category/all`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Unauthorized: Invalid or expired token');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();

        // Ensure we return an array of categories
        return Array.isArray(data) ? data : (data.Iraqi || []);
    } catch (error) {
        console.error('Error fetching category data:', error);
        throw error;
    }
};

// Recursively find a category by ID in the category tree
export const findCategoryById = (categories, categoryId) => {
    if (!categories || !Array.isArray(categories)) {
        return null;
    }

    for (const category of categories) {
        // Check if this is the category we're looking for
        if (category.id === categoryId) {
            return category;
        }

        // Check children if this category has them
        if (category.children && category.children.length > 0) {
            const foundInChildren = findCategoryById(category.children, categoryId);
            if (foundInChildren) {
                return foundInChildren;
            }
        }
    }

    return null;
}; 