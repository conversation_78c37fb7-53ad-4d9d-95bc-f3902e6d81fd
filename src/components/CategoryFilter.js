import { fetchCategories } from '../services/categoryService';
import { fetchBusinesses, displayBusinessPins } from '../services/businessService';
import { BASE_URL } from '../config/api';

class CategoryFilter {
    constructor(viewer) {
        this.categories = [];
        this.viewer = viewer;
        this.selectedCategories = new Set();
        this.businessCounts = new Map(); // Store category counts

        // Make this instance globally available
        window.categoryFilter = this;

        console.log('CategoryFilter: Initializing...');
        this.init().catch(error => {
            console.error('CategoryFilter: Initialization failed:', error);
        });
    }

    async init() {
        try {
            console.log('CategoryFilter: Starting initialization...');

            // Get the offcanvas body
            const offcanvasBody = document.querySelector('.offcanvas-body');
            if (!offcanvasBody) {
                throw new Error('Offcanvas body not found');
            }
            console.log('CategoryFilter: Found offcanvas body');

            // Add styles for offcanvas scrolling
            offcanvasBody.style.display = 'flex';
            offcanvasBody.style.flexDirection = 'column';
            offcanvasBody.style.height = '100%';

            // Create a scrollable container for filters
            const filterContainer = document.createElement('div');
            filterContainer.className = 'filter-container';
            filterContainer.style.flex = '1';
            filterContainer.style.overflowY = 'auto';
            filterContainer.style.padding = '0 16px';

            // Find the business-filter-section-placeholder
            const businessFilterPlaceholder = document.querySelector('.business-filter-section-placeholder');
            if (!businessFilterPlaceholder) {
                throw new Error('Business filter placeholder not found');
            }
            console.log('CategoryFilter: Found business filter placeholder');

            // Create container for categories
            const categoryContainer = document.createElement('div');
            categoryContainer.className = 'category-section';

            // Create main collapsible section for all business categories
            const mainCollapsible = document.createElement('div');
            mainCollapsible.className = 'main-category-collapsible';
            mainCollapsible.style.marginTop = '8px';
            mainCollapsible.style.marginBottom = '8px';

            // Create header for main collapsible
            const mainHeader = document.createElement('div');
            mainHeader.className = 'expandable-header';
            mainHeader.style.padding = '12px 16px';
            mainHeader.style.marginBottom = '0';
            mainHeader.style.borderRadius = '8px';
            mainHeader.style.backgroundColor = '#2b3035';
            mainHeader.style.border = '1px solid #373b3e';
            mainHeader.style.display = 'flex';
            mainHeader.style.justifyContent = 'space-between';
            mainHeader.style.alignItems = 'center';
            mainHeader.style.cursor = 'pointer';

            mainHeader.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-store"></i>
                    <span>Business</span>
                </div>
                <i class="fas fa-chevron-down expand-icon"></i>
            `;

            // Create content container for all categories
            const mainContent = document.createElement('div');
            mainContent.className = 'expandable-content';
            mainContent.style.margin = '8px 0';
            mainContent.style.padding = '8px';
            mainContent.style.backgroundColor = '#262a2f';
            mainContent.style.border = '1px solid #373b3e';
            mainContent.style.borderRadius = '4px';
            mainContent.style.display = 'none';

            // Add click handler for the main header
            mainHeader.addEventListener('click', () => {
                mainContent.style.display = mainContent.style.display === 'none' ? 'block' : 'none';
                mainHeader.querySelector('.expand-icon').classList.toggle('rotated');
            });

            // Fetch and render categories
            console.log('CategoryFilter: Fetching categories...');
            const data = await fetchCategories();
            this.categories = data;
            console.log('CategoryFilter: Categories fetched:', this.categories);

            if (Array.isArray(this.categories)) {
                this.categories.forEach((category, index) => {
                    const section = this.createExpandableSection(category);
                    if (index === 0) {
                        section.style.marginTop = '8px';
                    }
                    mainContent.appendChild(section);
                });
            }

            // Assemble the collapsible structure
            mainCollapsible.appendChild(mainHeader);
            mainCollapsible.appendChild(mainContent);
            categoryContainer.appendChild(mainCollapsible);

            // Insert the category container into the business filter placeholder
            businessFilterPlaceholder.appendChild(categoryContainer);
            console.log('CategoryFilter: Category container inserted into placeholder');

            console.log('CategoryFilter: Initialization complete');
        } catch (error) {
            console.error('CategoryFilter: Error during initialization:', error);
            throw error;
        }
    }

    createExpandableSection(category) {
        const sectionContainer = document.createElement('div');
        sectionContainer.className = 'expandable-section';
        sectionContainer.style.marginBottom = '8px';

        const header = document.createElement('div');
        header.className = 'expandable-header';
        header.style.padding = '12px 16px';
        header.style.marginBottom = '0';
        header.style.borderRadius = '8px';
        header.style.backgroundColor = '#2b3035';
        header.style.border = '1px solid #373b3e';
        header.style.display = 'flex';
        header.style.justifyContent = 'space-between';
        header.style.alignItems = 'center';
        header.style.cursor = 'pointer';

        const headerLeft = document.createElement('div');
        headerLeft.style.display = 'flex';
        headerLeft.style.alignItems = 'center';
        headerLeft.style.gap = '8px';
        headerLeft.innerHTML = `
            <i class="fas fa-folder" style="margin-right: 8px;"></i>
            <span>${category.name}</span>
        `;

        const expandIcon = document.createElement('i');
        expandIcon.className = 'fas fa-chevron-down expand-icon';

        header.appendChild(headerLeft);
        header.appendChild(expandIcon);

        const content = document.createElement('div');
        content.className = 'expandable-content';
        content.style.margin = '8px 0';
        content.style.padding = '8px';
        content.style.backgroundColor = '#262a2f';
        content.style.border = '1px solid #373b3e';
        content.style.borderRadius = '4px';

        header.addEventListener('click', () => {
            content.classList.toggle('show');
            header.querySelector('.expand-icon').classList.toggle('rotated');
        });

        if (category.children && category.children.length > 0) {
            category.children.forEach(child => {
                if (child.children && child.children.length > 0) {
                    // Create nested expandable section
                    const nestedSection = document.createElement('div');
                    nestedSection.className = 'nested-section';
                    nestedSection.style.marginLeft = '0';
                    nestedSection.style.marginTop = '0';
                    nestedSection.appendChild(this.createExpandableSection(child));
                    content.appendChild(nestedSection);
                } else {
                    // Create checkbox for leaf categories
                    const item = document.createElement('div');
                    item.className = 'form-check';
                    item.id = `category-item-${child.id}`;
                    item.style.padding = '12px 16px';
                    item.style.display = 'flex';
                    item.style.alignItems = 'center';
                    item.style.borderRadius = '4px';
                    item.style.cursor = 'pointer';
                    item.style.color = '#fff';
                    item.style.margin = '0';
                    item.style.transition = 'background-color 0.2s';
                    item.style.justifyContent = 'space-between';

                    // Add hover effect with slightly lighter color
                    item.addEventListener('mouseenter', () => {
                        item.style.backgroundColor = '#2b3035';
                    });
                    item.addEventListener('mouseleave', () => {
                        item.style.backgroundColor = 'transparent';
                    });

                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.className = 'form-check-input custom-checkbox';
                    checkbox.id = `category-${child.id}`;
                    checkbox.style.margin = '0 8px 0 0';
                    checkbox.style.backgroundColor = '#373b3e';
                    checkbox.style.borderColor = '#6c757d';
                    checkbox.addEventListener('change', () => this.handleCategoryChange(child.id, checkbox.checked));

                    const label = document.createElement('label');
                    label.className = 'form-check-label';
                    label.htmlFor = `category-${child.id}`;
                    label.textContent = child.name;
                    label.style.cursor = 'pointer';
                    label.style.userSelect = 'none';

                    // Create count badge
                    const countBadge = document.createElement('span');
                    countBadge.className = 'badge bg-secondary category-count';
                    countBadge.id = `category-count-${child.id}`;
                    countBadge.style.backgroundColor = '#373b3e';
                    countBadge.style.color = '#e9ecef';
                    countBadge.style.padding = '4px 8px';
                    countBadge.style.borderRadius = '4px';
                    countBadge.style.fontSize = '0.75rem';
                    countBadge.style.marginLeft = '8px';

                    // Set initial count if available
                    const count = this.businessCounts.get(child.id);
                    if (count !== undefined) {
                        countBadge.textContent = count;
                        countBadge.style.display = 'inline-block';
                    } else {
                        countBadge.style.display = 'none';
                    }

                    const DEFAULT_ICON_PATH = '/Icons/business.svg';

                    // Get authentication token
                    const token = localStorage.getItem('token');

                    const labelContainer = document.createElement('div');
                    labelContainer.style.display = 'flex';
                    labelContainer.style.alignItems = 'center';
                    labelContainer.style.flexGrow = '1';
                    labelContainer.appendChild(checkbox);
                    labelContainer.appendChild(label);
                    labelContainer.appendChild(countBadge); // Add count badge right after the label

                    const icon = document.createElement('img');
                    icon.src = child.icon_link && child.icon_link.trim() !== '' ? child.icon_link : DEFAULT_ICON_PATH;
                    icon.alt = `${child.name} icon`;
                    icon.style.width = '20px';
                    icon.style.height = '20px';
                    icon.style.marginLeft = 'auto'; // Push icon to the right edge

                    // Add event listener to add authentication token to the request
                    icon.addEventListener('error', () => {
                        icon.src = DEFAULT_ICON_PATH;
                    });

                    if (token && child.icon_link && child.icon_link.trim() !== '') {
                        // Create a fetch request with authentication token
                        fetch(child.icon_link, {
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        })
                            .then(response => {
                                if (response.ok) {
                                    return response.blob();
                                }
                                throw new Error('Failed to load icon');
                            })
                            .then(blob => {
                                const objectUrl = URL.createObjectURL(blob);
                                icon.src = objectUrl;
                            })
                            .catch(error => {
                                console.error('Error loading icon:', error);
                                icon.src = DEFAULT_ICON_PATH;
                            });
                    }

                    item.appendChild(labelContainer);
                    item.appendChild(icon);
                    content.appendChild(item);
                }
            });
        }

        sectionContainer.appendChild(header);
        sectionContainer.appendChild(content);
        return sectionContainer;
    }

    countLeafCategories(category) {
        if (!category.children || category.children.length === 0) {
            return 1;
        }
        return category.children.reduce((count, child) => count + this.countLeafCategories(child), 0);
    }

    async handleCategoryChange(categoryId, isChecked) {
        try {
            // Update selected categories set
            if (isChecked) {
                this.selectedCategories.add(categoryId);
            } else {
                this.selectedCategories.delete(categoryId);
            }

            if (isChecked) {
                // Get selected cities from ProvinceFilter if it exists
                let selectedCities = [];
                const provinceFilter = window.provinceFilter;
                if (provinceFilter && provinceFilter.selectedCities) {
                    selectedCities = Array.from(provinceFilter.selectedCities);
                }

                const businesses = await fetchBusinesses(categoryId, selectedCities);
                await displayBusinessPins(businesses, this.viewer, categoryId);
            } else {
                // Remove businesses for this category from both entities and dataSources
                this.viewer.dataSources.getByName('businesses')?.forEach(dataSource => {
                    const entitiesToRemove = dataSource.entities.values.filter(entity =>
                        entity.properties &&
                        entity.properties.getValue().categoryId === categoryId
                    );
                    entitiesToRemove.forEach(entity => dataSource.entities.remove(entity));
                });
            }
        } catch (error) {
            console.error('Error handling category change:', error);
        }
    }

    updateCategoryCounts(businessCounts) {
        // Clear previous counts
        this.businessCounts.clear();

        // Update with new counts
        if (Array.isArray(businessCounts)) {
            businessCounts.forEach(item => {
                if (item.category_id && item.count !== undefined) {
                    this.businessCounts.set(item.category_id, item.count);
                }
            });
        }

        // Update UI for each category
        this.categories.forEach(category => {
            this.updateCategoryCountUI(category);
        });
    }

    updateCategoryCountUI(category) {
        // Update leaf categories
        if (!category.children || category.children.length === 0) {
            const countBadge = document.getElementById(`category-count-${category.id}`);
            if (countBadge) {
                const count = this.businessCounts.get(category.id);
                if (count !== undefined) {
                    countBadge.textContent = count;
                    countBadge.style.display = 'inline-block';
                } else {
                    countBadge.style.display = 'none';
                }
            }
        } else {
            // Recursively update child categories
            category.children.forEach(child => {
                this.updateCategoryCountUI(child);
            });
        }
    }
}

export default CategoryFilter; 