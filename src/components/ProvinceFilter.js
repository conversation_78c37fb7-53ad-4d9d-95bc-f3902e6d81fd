import { fetchProvinces } from '../services/provinceService';
import { fetchBusinesses, displayBusinessPins, fetchBusinessCounts } from '../services/businessService';

class ProvinceFilter {
    constructor(viewer) {
        this.viewer = viewer;
        this.provinces = [];
        this.selectedProvinces = new Set();
        this.selectedCities = new Set();

        // Make this instance globally available
        window.provinceFilter = this;

        this.init().catch(error => {
            console.error('ProvinceFilter: Initialization failed:', error);
        });
    }

    async init() {
        try {
            console.log('ProvinceFilter: Starting initialization...');

            // Get the offcanvas body
            const offcanvasBody = document.querySelector('.offcanvas-body');
            if (!offcanvasBody) {
                throw new Error('Offcanvas body not found');
            }
            console.log('ProvinceFilter: Found offcanvas body');

            // Add styles for offcanvas scrolling
            offcanvasBody.style.display = 'flex';
            offcanvasBody.style.flexDirection = 'column';
            offcanvasBody.style.height = '100%';

            // Create a scrollable container for filters
            const filterContainer = document.createElement('div');
            filterContainer.className = 'filter-container';
            filterContainer.style.flex = '1';
            filterContainer.style.overflowY = 'auto';
            filterContainer.style.padding = '0 16px';

            // Find the province-filter-section-placeholder
            const provinceFilterPlaceholder = document.querySelector('.province-filter-section-placeholder');
            if (!provinceFilterPlaceholder) {
                throw new Error('Province filter placeholder not found');
            }
            console.log('ProvinceFilter: Found province filter placeholder');

            // Create container for provinces
            const provinceContainer = document.createElement('div');
            provinceContainer.className = 'province-section';

            // Create main collapsible section for all provinces
            const mainCollapsible = document.createElement('div');
            mainCollapsible.className = 'main-province-collapsible';
            mainCollapsible.style.marginTop = '8px';
            mainCollapsible.style.marginBottom = '8px';

            // Create header for main collapsible
            const mainHeader = document.createElement('div');
            mainHeader.className = 'expandable-header';
            mainHeader.style.padding = '12px 16px';
            mainHeader.style.marginBottom = '0';
            mainHeader.style.borderRadius = '8px';
            mainHeader.style.backgroundColor = '#2b3035';
            mainHeader.style.border = '1px solid #373b3e';
            mainHeader.style.display = 'flex';
            mainHeader.style.justifyContent = 'space-between';
            mainHeader.style.alignItems = 'center';
            mainHeader.style.cursor = 'pointer';
            mainHeader.style.transition = 'background-color 0.2s';

            // Add hover effect
            mainHeader.addEventListener('mouseenter', () => {
                mainHeader.style.backgroundColor = '#373b3e';
            });
            mainHeader.addEventListener('mouseleave', () => {
                mainHeader.style.backgroundColor = '#2b3035';
            });

            const headerLeft = document.createElement('div');
            headerLeft.style.display = 'flex';
            headerLeft.style.alignItems = 'center';
            headerLeft.style.gap = '8px';

            const icon = document.createElement('i');
            icon.className = 'fas fa-map-marker-alt';
            icon.style.color = '#fff'; // Matching the business filter icon color

            const headerText = document.createElement('span');
            headerText.textContent = 'Provinces';
            headerText.style.color = '#fff';
            headerText.style.fontWeight = '500';

            headerLeft.appendChild(icon);
            headerLeft.appendChild(headerText);

            const expandIcon = document.createElement('i');
            expandIcon.className = 'fas fa-chevron-down expand-icon';
            expandIcon.style.color = '#fff'; // Matching the business filter icon color
            expandIcon.style.transition = 'transform 0.2s ease-in-out';

            mainHeader.appendChild(headerLeft);
            mainHeader.appendChild(expandIcon);

            // Create content container for all provinces
            const mainContent = document.createElement('div');
            mainContent.className = 'expandable-content';
            mainContent.style.margin = '8px 0';
            mainContent.style.padding = '8px';
            mainContent.style.backgroundColor = '#262a2f';
            mainContent.style.border = '1px solid #373b3e';
            mainContent.style.borderRadius = '4px';
            mainContent.style.display = 'none';

            // Add click handler for the main header
            mainHeader.addEventListener('click', () => {
                mainContent.style.display = mainContent.style.display === 'none' ? 'block' : 'none';
                expandIcon.style.transform = mainContent.style.display === 'none' ? 'rotate(0deg)' : 'rotate(180deg)';
            });

            // Fetch and render provinces
            console.log('ProvinceFilter: Fetching provinces...');
            const data = await fetchProvinces();
            this.provinces = data;
            console.log('ProvinceFilter: Provinces fetched:', this.provinces);

            if (Array.isArray(this.provinces)) {
                this.provinces.forEach((province, index) => {
                    const section = this.createExpandableSection(province);
                    if (index === 0) {
                        section.style.marginTop = '8px';
                    }
                    mainContent.appendChild(section);
                });
            }

            // Assemble the collapsible structure
            mainCollapsible.appendChild(mainHeader);
            mainCollapsible.appendChild(mainContent);
            provinceContainer.appendChild(mainCollapsible);

            // Insert the province container into the province filter placeholder
            provinceFilterPlaceholder.appendChild(provinceContainer);
            console.log('ProvinceFilter: Province container inserted into placeholder');

            // Update business counts based on any initially selected cities
            this.updateBusinessCounts();

            console.log('ProvinceFilter: Initialization complete');
        } catch (error) {
            console.error('ProvinceFilter: Error during initialization:', error);
            throw error;
        }
    }

    createExpandableSection(province) {
        const sectionContainer = document.createElement('div');
        sectionContainer.className = 'expandable-section';
        sectionContainer.style.marginBottom = '8px';

        const header = document.createElement('div');
        header.className = 'expandable-header';
        header.style.padding = '12px 16px';
        header.style.marginBottom = '0';
        header.style.borderRadius = '8px';
        header.style.backgroundColor = '#2b3035';
        header.style.border = '1px solid #373b3e';
        header.style.display = 'flex';
        header.style.justifyContent = 'space-between';
        header.style.alignItems = 'center';
        header.style.cursor = 'pointer';
        header.style.transition = 'background-color 0.2s';

        header.addEventListener('mouseenter', () => {
            header.style.backgroundColor = '#373b3e';
        });
        header.addEventListener('mouseleave', () => {
            header.style.backgroundColor = '#2b3035';
        });

        const headerLeft = document.createElement('div');
        headerLeft.style.display = 'flex';
        headerLeft.style.alignItems = 'center';
        headerLeft.style.gap = '8px';

        // Add province icon
        const provinceIcon = document.createElement('i');
        provinceIcon.className = 'fas fa-folder';
        provinceIcon.style.marginRight = '8px';
        provinceIcon.style.color = '#fff'; // Matching the business filter icon color

        const label = document.createElement('span');
        label.textContent = province.name;
        label.style.color = '#fff';
        label.style.cursor = 'pointer';
        label.style.userSelect = 'none';
        label.style.fontWeight = '500';

        headerLeft.appendChild(provinceIcon);
        headerLeft.appendChild(label);

        const expandIcon = document.createElement('i');
        expandIcon.className = 'fas fa-chevron-down expand-icon';
        expandIcon.style.color = '#fff'; // Matching the business filter icon color
        expandIcon.style.transition = 'transform 0.2s ease-in-out';

        header.appendChild(headerLeft);
        header.appendChild(expandIcon);

        const content = document.createElement('div');
        content.className = 'expandable-content';
        content.style.margin = '8px 0';
        content.style.padding = '8px';
        content.style.backgroundColor = '#262a2f';
        content.style.border = '1px solid #373b3e';
        content.style.borderRadius = '4px';
        content.style.display = 'none';

        // Create city items immediately since we have the data
        if (province.Cities && Array.isArray(province.Cities)) {
            province.Cities.forEach(city => {
                const item = document.createElement('div');
                item.className = 'form-check';
                item.style.padding = '12px 16px';
                item.style.display = 'flex';
                item.style.alignItems = 'center';
                item.style.justifyContent = 'space-between';
                item.style.borderRadius = '4px';
                item.style.cursor = 'pointer';
                item.style.color = '#fff';
                item.style.margin = '0';
                item.style.transition = 'background-color 0.2s';

                item.addEventListener('mouseenter', () => {
                    item.style.backgroundColor = '#2b3035';
                });
                item.addEventListener('mouseleave', () => {
                    item.style.backgroundColor = 'transparent';
                });

                const labelContainer = document.createElement('div');
                labelContainer.style.display = 'flex';
                labelContainer.style.alignItems = 'center';
                labelContainer.style.gap = '8px';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'form-check-input custom-checkbox';
                checkbox.id = `city-${city.id}`;
                checkbox.style.margin = '0';
                checkbox.style.backgroundColor = '#373b3e';
                checkbox.style.borderColor = '#6c757d';
                checkbox.addEventListener('change', () => this.handleCityChange(city.id, checkbox.checked));

                const label = document.createElement('label');
                label.className = 'form-check-label';
                label.htmlFor = `city-${city.id}`;
                label.textContent = city.name;
                label.style.cursor = 'pointer';
                label.style.userSelect = 'none';
                label.style.color = '#e9ecef'; // Lighter text color for city items

                labelContainer.appendChild(checkbox);
                labelContainer.appendChild(label);
                item.appendChild(labelContainer);

                // Add business count badge if available
                if (city.businesses_count !== undefined) {
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-secondary';
                    badge.textContent = city.businesses_count;
                    badge.style.backgroundColor = '#373b3e';
                    badge.style.color = '#e9ecef';
                    badge.style.padding = '4px 8px';
                    badge.style.borderRadius = '4px';
                    badge.style.fontSize = '0.75rem';
                    item.appendChild(badge);
                }

                content.appendChild(item);
            });
        }

        header.addEventListener('click', () => {
            // Toggle content visibility
            const wasHidden = content.style.display === 'none';
            content.style.display = wasHidden ? 'block' : 'none';
            expandIcon.style.transform = wasHidden ? 'rotate(180deg)' : 'rotate(0deg)';
        });

        // Add business count badge to province if available
        if (province.businesses_count !== undefined) {
            const badge = document.createElement('span');
            badge.className = 'badge bg-secondary';
            badge.textContent = province.businesses_count;
            badge.style.backgroundColor = '#373b3e';
            badge.style.color = '#e9ecef';
            badge.style.padding = '4px 8px';
            badge.style.borderRadius = '4px';
            badge.style.fontSize = '0.75rem';
            badge.style.marginLeft = '8px';
            headerLeft.appendChild(badge);
        }

        sectionContainer.appendChild(header);
        sectionContainer.appendChild(content);

        return sectionContainer;
    }

    handleProvinceChange(provinceId, isChecked) {
        if (isChecked) {
            this.selectedProvinces.add(provinceId);

            // Find the province data to get all its cities
            const province = this.provinces.find(p => p.id === provinceId);
            if (province && province.Cities) {
                // Add all cities from this province
                province.Cities.forEach(city => {
                    this.selectedCities.add(city.id);
                    // Update the city checkbox UI
                    const cityCheckbox = document.querySelector(`#city-${city.id}`);
                    if (cityCheckbox) {
                        cityCheckbox.checked = true;
                    }
                });
            }
        } else {
            this.selectedProvinces.delete(provinceId);

            // Find the province data to get all its cities
            const province = this.provinces.find(p => p.id === provinceId);
            if (province && province.Cities) {
                // Remove all cities from this province
                province.Cities.forEach(city => {
                    this.selectedCities.delete(city.id);
                    // Update the city checkbox UI
                    const cityCheckbox = document.querySelector(`#city-${city.id}`);
                    if (cityCheckbox) {
                        cityCheckbox.checked = false;
                    }
                });
            }
        }

        // Update business counts based on selected cities
        this.updateBusinessCounts();

        // Get all currently selected business categories
        const categoryFilter = window.categoryFilter;
        if (categoryFilter && categoryFilter.selectedCategories.size > 0) {
            // Convert selected categories to array
            const selectedCategories = Array.from(categoryFilter.selectedCategories);

            // Get the selected cities array
            const selectedCities = Array.from(this.selectedCities);

            // For each selected category, fetch and display businesses
            selectedCategories.forEach(async (categoryId) => {
                try {
                    // Fetch businesses with both category ID and selected city IDs
                    const businesses = await fetchBusinesses(categoryId, selectedCities);

                    // Display the fetched businesses on the map
                    await displayBusinessPins(businesses, this.viewer, categoryId);
                } catch (error) {
                    console.error(`Error fetching businesses for category ${categoryId} and cities ${selectedCities}:`, error);
                }
            });
        } else {
            // If no categories are selected, just update visibility
            this.updateVisibility();
        }
    }

    handleCityChange(cityId, isChecked) {
        if (isChecked) {
            this.selectedCities.add(cityId);
        } else {
            this.selectedCities.delete(cityId);
        }

        // Update business counts for all categories based on selected cities
        this.updateBusinessCounts();

        // Get all currently selected business categories
        const categoryFilter = window.categoryFilter;
        if (categoryFilter && categoryFilter.selectedCategories.size > 0) {
            // Convert selected categories to array
            const selectedCategories = Array.from(categoryFilter.selectedCategories);

            // Get the selected cities array
            const selectedCities = Array.from(this.selectedCities);

            // For each selected category, fetch and display businesses
            selectedCategories.forEach(async (categoryId) => {
                try {
                    // Fetch businesses with both category ID and selected city IDs
                    const businesses = await fetchBusinesses(categoryId, selectedCities);

                    // Display the fetched businesses on the map
                    await displayBusinessPins(businesses, this.viewer, categoryId);
                } catch (error) {
                    console.error(`Error fetching businesses for category ${categoryId} and cities ${selectedCities}:`, error);
                }
            });
        } else {
            // If no categories are selected, just update visibility
            this.updateVisibility();
        }

        if (!isChecked) {
            // Remove pins for the unselected city
            this.viewer.dataSources.getByName('businesses')?.forEach(dataSource => {
                const entitiesToRemove = dataSource.entities.values.filter(entity =>
                    entity.properties &&
                    entity.properties.getValue().city_id === cityId
                );
                entitiesToRemove.forEach(entity => {
                    dataSource.entities.remove(entity);
                });
            });
        }
    }

    async updateBusinessCounts() {
        try {
            // Get the selected cities array
            const selectedCities = Array.from(this.selectedCities);

            // If no cities are selected, clear counts
            if (selectedCities.length === 0) {
                const categoryFilter = window.categoryFilter;
                if (categoryFilter) {
                    categoryFilter.updateCategoryCounts([]);
                }
                return;
            }

            // Fetch business counts for selected cities
            const businessCounts = await fetchBusinessCounts(selectedCities);

            // Update the category filter with the counts
            const categoryFilter = window.categoryFilter;
            if (categoryFilter) {
                categoryFilter.updateCategoryCounts(businessCounts);
            }
        } catch (error) {
            console.error('Error updating business counts:', error);
        }
    }

    updateVisibility() {
        // Check if any categories are selected
        const categoryFilter = window.categoryFilter;
        if (!categoryFilter || categoryFilter.selectedCategories.size === 0) {
            return;
        }

        // Find all checked category checkboxes and trigger their change event
        const categoryCheckboxes = document.querySelectorAll('.category-section .form-check-input:checked');
        categoryCheckboxes.forEach(checkbox => {
            const categoryId = parseInt(checkbox.id.replace('category-', ''));
            // Get the CategoryFilter instance
            const categoryFilter = window.categoryFilter;
            if (categoryFilter) {
                categoryFilter.handleCategoryChange(categoryId, true);
            }
        });
    }
}

export default ProvinceFilter; 