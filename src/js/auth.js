import { authService } from '../services/authService.js';

document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorMessage = document.getElementById('errorMessage');

    try {
        await authService.login(username, password);
        window.location.href = 'index.html';
    } catch (error) {
        errorMessage.style.display = 'block';
    }
}); 