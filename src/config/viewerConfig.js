import { Cartesian3, Terrain, Viewer } from 'cesium';

export const createViewer = (containerId) => {
    // Create the viewer
    const viewer = new Viewer(containerId, {
        terrain: Terrain.fromWorldTerrain(),
        shouldAnimate: true,
        imageryProvider: false,
        baseLayerPicker: true,
        timeline: false,
        animation: false,
    });

    // Wait for viewer to be ready before configuration
    viewer.scene.globe.tileLoadProgressEvent.addEventListener(() => {
        setupViewer(viewer);
    });

    return viewer;
};

const setupViewer = (viewer) => {
    // Remove cesium logo and credits (safely)
    try {
        const credits = document.querySelector(".cesium-widget-credits");
        if (credits) {
            credits.remove();
        }
    } catch (error) {
        console.warn('Could not remove credits:', error);
    }

    // Set default view
    viewer.homeButton.viewModel.command.beforeExecute.addEventListener((commandInfo) => {
        viewer.scene.camera.flyTo({
            destination: Cartesian3.fromDegrees(43.6793, 33.2232, 1500000),
        });
        commandInfo.cancel = true;
    });

    // Set Bing Maps Aerial with Labels
    viewer.baseLayerPicker.viewModel.selectedImagery =
        viewer.baseLayerPicker.viewModel.imageryProviderViewModels.find(
            (model) => model.name === "Bing Maps Aerial with Labels"
        );

    // Disable terrain depth test for better visualization
    viewer.scene.globe.depthTestAgainstTerrain = false;

    // Set initial camera position only once at startup
    if (!viewer.scene.camera.initialized) {
        viewer.scene.camera.flyTo({
            destination: Cartesian3.fromDegrees(43.6793, 33.2232, 1500000),
        });
        viewer.scene.camera.initialized = true;
    }
}; 