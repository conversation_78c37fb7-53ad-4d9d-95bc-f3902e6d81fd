html,
body,
#cesiumContainer {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}




*,
body {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.h-100 {
    height: 100vh !important;
}

.bold-hr {
    border: 0;
    height: 1px;
    background-color: #858585;
}

/* Logout button styles */
.logout-button {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    width: 80%;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.logout-button:hover {
    background-color: #b31b2b;
}

.province-section {
    margin-top: 16px;
}

.province-section .expandable-section {
    margin-bottom: 8px;
}

.province-section .expandable-header {
    padding: 12px 16px;
    margin-bottom: 0;
    border-radius: 8px;
    background-color: #2b3035;
    border: 1px solid #373b3e;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.province-section .expandable-header:hover {
    background-color: #373b3e;
}

.province-section .expandable-content {
    display: none;
    padding: 8px;
    margin: 8px 0;
    background-color: #262a2f;
    border: 1px solid #373b3e;
    border-radius: 4px;
}

.province-section .expandable-content.show {
    display: block;
}

.province-section .form-check {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    cursor: pointer;
    color: #fff;
    margin: 0;
    transition: background-color 0.2s;
}

.province-section .form-check:hover {
    background-color: #2b3035;
}

.province-section .form-check-input {
    margin: 0 8px 0 0;
    background-color: #373b3e;
    border-color: #6c757d;
}

.province-section .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.province-section .form-check-label {
    cursor: pointer;
    user-select: none;
}

.province-section .expand-icon {
    transition: transform 0.2s;
}

.province-section .expand-icon.rotated {
    transform: rotate(180deg);
}

.switch-item .switch-header {
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    background-color: #2b3035;
    border: 1px solid #373b3e;
    margin-bottom: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.switch-item .switch-header:hover {
    background-color: #373b3e;
}

.switch-item .expandable-content {
    padding: 8px;
    margin: 8px 0;
    background-color: #262a2f;
    border: 1px solid #373b3e;
    border-radius: 4px;
}