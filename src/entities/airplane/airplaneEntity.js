import { Cartesian3, Heading<PERSON>itchR<PERSON>, Transforms, DistanceDisplayCondition, Math as CesiumMath } from 'cesium';

export class AirplaneEntity {
    constructor(viewer, initialPosition) {
        this.viewer = viewer;
        this.position = initialPosition;
        this.entity = this.createEntity();
    }

    calculateOrientation() {
        return Transforms.headingPitchRollQuaternion(
            Cartesian3.fromDegrees(
                this.position.longitude,
                this.position.latitude,
                this.position.height
            ),
            new HeadingPitchRoll(
                CesiumMath.toRadians(90), // Initial heading (eastward)
                0, // Pitch
                0  // Roll
            )
        );
    }

    getDescription() {
        return `
            <div style="font-family: Arial, sans-serif; line-height: 1.5;">
                <h2 style="font-size: 18px; margin: 0; padding: 0;">✈️ Airplane</h2>
                <p style="font-size: 14px; margin-top: 10px;">
                    The <strong>Airplane</strong> entity represents commercial and private aircraft flying at various altitudes across the globe. These airplanes are critical for connecting cities, countries, and continents, making air travel the fastest mode of transportation available.
                </p>
                <ul style="font-size: 14px; padding-left: 20px; margin: 10px 0;">
                    <li>Speed: Can travel at up to 900 km/h (560 mph) for commercial jets.</li>
                    <li>Altitude: Typically cruises at an altitude of 30,000–40,000 feet.</li>
                    <li>Range: Modern airplanes can cover distances of over 15,000 kilometers (9,320 miles).</li>
                    <li>Usage: Includes passenger transport, cargo delivery, and military applications.</li>
                </ul>
                <p style="font-size: 14px; margin-top: 10px;">
                    Airplanes revolutionized global transportation and logistics, enabling quick access to remote areas and facilitating international trade and tourism.
                </p>
            </div>
        `;
    }

    createEntity() {
        return this.viewer.entities.add({
            name: "Airplane",
            position: Cartesian3.fromDegrees(
                this.position.longitude,
                this.position.latitude,
                this.position.height
            ),
            orientation: this.calculateOrientation(),
            model: {
                uri: "/Model/AirPlane.glb",
                minimumPixelSize: 128,
                maximumScale: 20000,
                distanceDisplayCondition: new DistanceDisplayCondition(1600000, 4000000),
            },
            description: this.getDescription()
        });
    }

    updatePosition(longitude, latitude, height) {
        const position = Cartesian3.fromDegrees(longitude, latitude, height);
        this.entity.position = position;

        const hpr = new HeadingPitchRoll(
            CesiumMath.toRadians(-35), // Heading towards east
            0, // No pitch
            0  // No roll
        );
        this.entity.orientation = Transforms.headingPitchRollQuaternion(position, hpr);
    }

    // ... Additional methods for airplane control
} 