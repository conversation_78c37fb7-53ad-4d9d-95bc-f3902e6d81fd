import { <PERSON><PERSON>ty, Cartesian3, VerticalOrigin, HeightReference } from 'cesium';

export class BusinessEntity {
    constructor(business, defaultPin, groupKey) {
        this.business = business;
        this.defaultPin = defaultPin;
        this.groupKey = groupKey;
    }

    create() {
        return new Entity({
            name: this.business.name,
            description: this.business.description,
            position: Cartesian3.fromDegrees(
                this.business.longitude,
                this.business.latitude,
                0
            ),
            billboard: {
                image: this.defaultPin,
                verticalOrigin: VerticalOrigin.BOTTOM,
                heightReference: HeightReference.CLAMP_TO_GROUND,
                show: true,
                distanceDisplayCondition: undefined,
                disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            properties: {
                group: this.groupKey,
                id: this.business.id,
                entityType: 'business'
            }
        });
    }
} 