export const setupEventListeners = (viewer) => {
    // Business visibility toggle
    const businessSwitch = document.getElementById("isBusiness");
    businessSwitch?.addEventListener("change", event => {
        const businessEntities = viewer.dataSources.getByName('businesses')[0]?.entities;
        if (businessEntities) {
            businessEntities.values.forEach(entity => {
                entity.show = event.target.checked;
            });
        }
    });

    // Airplane visibility toggle
    const airplaneSwitch = document.getElementById("isAirplanes");
    airplaneSwitch?.addEventListener("change", event => {
        const airplane = viewer.entities.getById('airplane');
        if (airplane) {
            airplane.show = event.target.checked;
        }
    });

    // Satellite visibility toggle
    const satelliteSwitch = document.getElementById("isSatellites");
    satelliteSwitch?.addEventListener("change", event => {
        const iss = viewer.entities.getById('iss');
        if (iss) {
            iss.show = event.target.checked;
        }
    });

    // Population visibility toggle
    const populationSwitch = document.getElementById("isPopulation");

    if (!populationSwitch) {
        console.error("Population switch element not found!");
        return;
    }

    populationSwitch.addEventListener("change", event => {
        const allEntities = viewer.entities.values;
        const populationEntities = allEntities.filter(entity => {
            const isPopulation = entity.properties && entity.properties.group === "population";
            return isPopulation;
        });

        populationEntities.forEach(entity => {
            entity.show = event.target.checked;
        });
    });

    // Roads visibility toggle
    const roadsSwitch = document.getElementById("isRoads");
    if (roadsSwitch) {
        roadsSwitch.addEventListener("change", event => {
            const roadDataSources = viewer.dataSources.getByName('roads');
            if (roadDataSources && roadDataSources.length > 0) {
                roadDataSources.forEach(dataSource => {
                    dataSource.show = event.target.checked;
                });
            }
        });
    }
}; 