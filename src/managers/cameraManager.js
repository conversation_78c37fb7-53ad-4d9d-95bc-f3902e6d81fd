/**
 * Camera Manager - <PERSON>les keyboard controls for camera movement in CesiumJS
 * 
 * Controls:
 * W, Up Arrow: Move forward
 * S, Down Arrow: Move backward
 * A, Left Arrow: Move left
 * D, Right Arrow: Move right
 * Q: Move up
 * E: Move down
 * H: Return to home position
 */

import * as Cesium from 'cesium';

// Movement settings
const SETTINGS = {
    moveRate: 25.0,         // Base movement rate in meters per second (reduced from 100.0)
    moveRateFactor: 0.01,   // Factor to adjust movement rate based on height (reduced from 0.1)
    minHeight: 10.0,         // Minimum height to maintain above terrain (meters)
    smoothFactor: 0.9       // Smoothing factor for movement (0-1)
};

// Key state tracking
const keyState = {
    w: false, a: false, s: false, d: false,
    q: false, e: false, h: false,
    up: false, down: false, left: false, right: false,
    shift: false  // For speed boost
};

// Last update time
let lastTime = null;

// Active viewer reference
let activeViewer = null;

/**
 * Initialize camera controls
 * @param {Cesium.Viewer} viewer - The Cesium viewer instance
 */
export function initializeCameraControls(viewer) {
    if (!viewer) {
        console.error('Camera controls initialization failed: viewer is undefined');
        return;
    }

    activeViewer = viewer;
    console.log('Initializing keyboard camera controls');

    // Set up key event listeners
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    // Set up the render loop for smooth movement
    viewer.scene.preRender.addEventListener(updateCamera);

    // Add click handler to ensure focus on the canvas
    const canvas = viewer.canvas;
    canvas.setAttribute('tabindex', '0'); // needed to put focus on the canvas
    canvas.onclick = function () {
        canvas.focus();
    };

    console.log('Keyboard camera controls initialized');
}

/**
 * Handle key down events
 * @param {KeyboardEvent} event - The keyboard event
 */
function handleKeyDown(event) {
    // Don't handle keys when typing in form elements
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }

    // Prevent default behavior for navigation keys
    if (['w', 'a', 's', 'd', 'q', 'e', 'h', 'arrowup', 'arrowdown', 'arrowleft', 'arrowright'].includes(event.key.toLowerCase())) {
        event.preventDefault();
    }

    switch (event.key.toLowerCase()) {
        case 'w': keyState.w = true; break;
        case 'a': keyState.a = true; break;
        case 's': keyState.s = true; break;
        case 'd': keyState.d = true; break;
        case 'q': keyState.q = true; break;
        case 'e': keyState.e = true; break;
        case 'h':
            keyState.h = true;
            // Go to home position immediately when H is pressed
            if (activeViewer) {
                goToHome();
            }
            break;
        case 'arrowup': keyState.up = true; break;
        case 'arrowdown': keyState.down = true; break;
        case 'arrowleft': keyState.left = true; break;
        case 'arrowright': keyState.right = true; break;
        case 'shift': keyState.shift = true; break;
    }
}

/**
 * Handle key up events
 * @param {KeyboardEvent} event - The keyboard event
 */
function handleKeyUp(event) {
    switch (event.key.toLowerCase()) {
        case 'w': keyState.w = false; break;
        case 'a': keyState.a = false; break;
        case 's': keyState.s = false; break;
        case 'd': keyState.d = false; break;
        case 'q': keyState.q = false; break;
        case 'e': keyState.e = false; break;
        case 'h': keyState.h = false; break;
        case 'arrowup': keyState.up = false; break;
        case 'arrowdown': keyState.down = false; break;
        case 'arrowleft': keyState.left = false; break;
        case 'arrowright': keyState.right = false; break;
        case 'shift': keyState.shift = false; break;
    }
}

/**
 * Update camera position based on key states
 */
function updateCamera() {
    if (!activeViewer) return;

    const camera = activeViewer.camera;
    const now = Date.now();

    // Calculate delta time for smooth movement
    if (!lastTime) {
        lastTime = now;
        return;
    }

    const deltaTime = Math.min((now - lastTime) / 1000, 0.1); // Convert to seconds, cap at 0.1s
    lastTime = now;

    // Get camera height for speed adjustment
    const ellipsoid = activeViewer.scene.globe.ellipsoid;
    const cameraHeight = ellipsoid.cartesianToCartographic(camera.position).height;

    // Adjust movement speed based on height and shift key (for boost)
    const speedMultiplier = keyState.shift ? 5.0 : 1.0; // 5x speed when holding Shift
    const moveSpeed = SETTINGS.moveRate * speedMultiplier * (1 + cameraHeight * SETTINGS.moveRateFactor) * deltaTime;

    // Process movement keys
    let moveForward = 0;
    let moveRight = 0;
    let moveUp = 0;

    // Forward/backward movement
    if (keyState.w || keyState.up) moveForward += moveSpeed;
    if (keyState.s || keyState.down) moveForward -= moveSpeed;

    // Left/right movement
    if (keyState.d || keyState.right) moveRight += moveSpeed;
    if (keyState.a || keyState.left) moveRight -= moveSpeed;

    // Up/down movement
    if (keyState.q) moveUp += moveSpeed;
    if (keyState.e) moveUp -= moveSpeed;

    // Apply movements if any keys are pressed
    if (moveForward !== 0) {
        camera.moveForward(moveForward);
    }

    if (moveRight !== 0) {
        camera.moveRight(moveRight);
    }

    if (moveUp !== 0) {
        camera.moveUp(moveUp);
    }

    // Maintain minimum height above terrain if terrain provider is available
    if (activeViewer.scene.terrainProvider && !activeViewer.scene.terrainProvider.isCreateMeshTaskProcessor) {
        const globe = activeViewer.scene.globe;
        const cartographic = ellipsoid.cartesianToCartographic(camera.position);

        if (globe.getHeight) {
            const terrainHeight = globe.getHeight(cartographic);
            if (terrainHeight !== undefined) {
                const heightAboveTerrain = cartographic.height - terrainHeight;
                if (heightAboveTerrain < SETTINGS.minHeight) {
                    cartographic.height = terrainHeight + SETTINGS.minHeight;
                    const newPosition = ellipsoid.cartographicToCartesian(cartographic);
                    camera.position = newPosition;
                }
            }
        }
    }
}

/**
 * Move camera to home position (Iraq)
 */
function goToHome() {
    if (!activeViewer) return;

    activeViewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(43.6793, 33.2232, 1500000),
    });
}

/**
 * Clean up event listeners when no longer needed
 */
export function cleanupCameraControls() {
    if (activeViewer) {
        activeViewer.scene.preRender.removeEventListener(updateCamera);
        activeViewer = null;
    }

    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keyup', handleKeyUp);

    console.log('Camera controls cleaned up');
} 