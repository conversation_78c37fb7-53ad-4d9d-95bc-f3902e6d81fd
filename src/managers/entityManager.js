import { Color, Cartesian3, DistanceDisplayCondition, PinBuilder } from 'cesium';
import { preparePopulationData } from '../services/populationService';

// Create an entity manager instance
export const entityManager = {
    add: function (entity, viewer) {
        viewer.entities.add(entity);
    },

    removeByGroup: function (groupKey, viewer) {
        const entities = viewer.entities.values;
        for (let i = entities.length - 1; i >= 0; i--) {
            const entity = entities[i];
            if (entity.properties && entity.properties.getValue().group === groupKey) {
                viewer.entities.remove(entity);
            }
        }
    }
};

export const initializeEntities = async (viewer) => {
    try {
        // Initialize PinBuilder
        const pinBuilder = new PinBuilder();
        const defaultPin = pinBuilder.fromUrl("/Icons/business.svg", Color.BLACK, 48);

        // // Initialize businesses
        // const businesses = await fetchBusinesses();
        // const businessEntities = businesses.map(business =>
        //     new BusinessEntity(business, defaultPin, "businesses").create()
        // );

        // Initialize population data
        const populationData = await preparePopulationData();


        populationData.forEach(data => {
            viewer.entities.add({
                position: Cartesian3.fromDegrees(data.longitude, data.latitude, 0),
                point: {
                    color: Color.fromBytes(207, 109, 73, 205),
                    outlineColor: Color.fromBytes(165, 87, 58, 255),
                    outlineWidth: 2,
                    pixelSize: data.pixelSize,
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    distanceDisplayCondition: new DistanceDisplayCondition(400000, 2700000)
                },
                properties: {
                    group: "population"
                },
                name: `Iraq - ${data.name}`,
                description: `
                    <p>City Name: ${data.name}</p>
                    <p>Population: ${data.population}</p>
                    <p>Description: ${data.description || "No additional data available."}</p>
                `
            });
        });

        return;

        // return {
        // airplane: airplane.entity,
        // businesses: businessEntities
        // };
    } catch (error) {
        console.error('Error initializing entities:', error);
        throw error;
    }
}; 