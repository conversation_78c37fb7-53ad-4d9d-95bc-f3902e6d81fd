import { Color, EntityCluster } from 'cesium';
import { modernColors } from '../constants/modernColors';

export function setupClustering(viewer, enabled = true) {
    const entityCluster = viewer.entities;
    entityCluster.clustering.enabled = true;
    entityCluster.clustering.pixelRange = 50;
    entityCluster.clustering.minimumClusterSize = 2;

    // Create the cluster event callback function
    const clusterEventCallback = (clusteredEntities, cluster) => {
        cluster.label.show = false;
        cluster.billboard.show = true;
        cluster.billboard.verticalOrigin = 1;
        cluster.billboard.horizontalOrigin = 0;

        const count = clusteredEntities.length;
        cluster.billboard.image = createClusterLabel(count);
    };

    // Remove existing event listener if it exists
    entityCluster.clustering.clusterEvent.removeEventListener(clusterEventCallback);

    // Add the new event listener
    entityCluster.clustering.clusterEvent.addEventListener(clusterEventCallback);
}

// Helper function to create cluster labels
function createClusterLabel(count) {
    const canvas = document.createElement('canvas');
    canvas.width = 40;
    canvas.height = 40;
    const context = canvas.getContext('2d');

    // Draw circle background
    context.beginPath();
    context.arc(20, 20, 18, 0, 2 * Math.PI, false);
    context.fillStyle = '#ff7f50';
    context.fill();
    context.strokeStyle = '#ffffff';
    context.lineWidth = 2;
    context.stroke();

    // Draw text
    context.fillStyle = '#ffffff';
    context.font = 'bold 18px Arial';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(count.toString(), 20, 20);

    return canvas;
}
