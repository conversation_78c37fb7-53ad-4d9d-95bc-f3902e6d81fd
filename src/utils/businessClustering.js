import { Color, PinBuilder, HeightReference, VerticalOrigin } from 'cesium';
import { modernColors } from '../constants/modernColors';

const pinBuilder = new PinBuilder();

// Create cluster pins with different sizes based on count
const createClusterPin = (text, size) => {
    return pinBuilder.fromText(text, modernColors.CLUSTER_ICON_COLOR, size).toDataURL();
};

// Cache cluster pins for better performance
const clusterPins = {
    10000: createClusterPin("10K+", 64),
    2000: createClusterPin("2K+", 64),
    500: createClusterPin("500+", 64),
    100: createClusterPin("100+", 64),
    20: createClusterPin("20+", 48),
    9: createClusterPin("9+", 48)
};

// Create single digit pins (2-8)
const singleDigitPins = Array.from({ length: 7 }, (_, i) =>
    pinBuilder.fromText(`${i + 2}`, modernColors.CLUSTER_ICON_COLOR, 48).toDataURL()
);

// Get appropriate cluster icon based on count
const getClusterIcon = (count) => {
    if (count >= 10000) return clusterPins[10000];
    if (count >= 2000) return clusterPins[2000];
    if (count >= 500) return clusterPins[500];
    if (count >= 100) return clusterPins[100];
    if (count >= 20) return clusterPins[20];
    if (count >= 9) return clusterPins[9];
    return singleDigitPins[count - 2]; // For counts 2-8
};

export const setupBusinessClustering = (dataSource) => {
    const clustering = dataSource.clustering;

    // Configure clustering
    clustering.enabled = true;
    clustering.minimumClusterSize = 2;
    clustering.pixelRange = 50;

    // Handle cluster appearance
    clustering.clusterEvent.addEventListener((clusteredEntities, cluster) => {
        cluster.billboard.heightReference = HeightReference.CLAMP_TO_GROUND;
        cluster.billboard.verticalOrigin = VerticalOrigin.BOTTOM;

        // Hide default label
        cluster.label.show = false;

        // Configure cluster billboard
        cluster.billboard.show = true;
        cluster.billboard.id = cluster.label.id;

        const count = clusteredEntities.length;
        cluster.billboard.image = getClusterIcon(count);
    });
}; 