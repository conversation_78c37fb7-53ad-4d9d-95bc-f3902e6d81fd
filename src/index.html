<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        #loadingOverlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 9999;
        }

        .earth {
            width: 400px;
            height: 400px;
            background-image: url('https://upload.wikimedia.org/wikipedia/commons/thumb/9/97/The_Earth_seen_from_Apollo_17.jpg/600px-The_Earth_seen_from_Apollo_17.jpg');
            background-size: cover;
            border-radius: 50%;
            animation: rotate 5s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        .fade-out {
            opacity: 0;
            transition: opacity 1s ease-out;
        }

        /* New styles for switch section */
        .switch-section {
            margin-top: 1rem;
        }

        .switch-item {
            margin-bottom: 8px;
        }

        .switch-header {
            width: 100%;
            text-align: left;
            background-color: #2b3035;
            border: 1px solid #373b3e;
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s;
        }

        .switch-header:hover {
            background-color: #373b3e;
        }

        .custom-checkbox {
            width: 20px;
            height: 20px;
            background-color: #373b3e;
            border: 1px solid #6c757d;
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            transition: all 0.2s ease;
        }

        .custom-checkbox:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .custom-checkbox:checked::after {
            content: '✓';
            position: absolute;
            color: white;
            font-size: 14px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .custom-checkbox:hover {
            border-color: #0d6efd;
        }

        .logout-container {
            position: sticky;
            bottom: 0;
            background: #212529;
            padding: 1rem;
            margin-top: auto;
            border-top: 1px solid #373b3e;
        }

        .filter-container {
            flex: 1;
            overflow-y: auto;
            padding-right: 10px;
            margin-bottom: 1rem;
        }

        .filter-divider {
            height: 1px;
            background-color: #ddd;
            margin: 8px 0;
        }

        .logout-button {
            color: #d32f2f;
            margin-top: 8px;
        }

        .logout-button:hover {
            background-color: #ffebee;
        }

        /* Category Filter Styles */
        .category-section .expandable-header {
            width: 100%;
            text-align: left;
            background-color: #2b3035;
            border: 1px solid #373b3e;
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 4px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s;
        }

        .category-section .expandable-header:hover {
            background-color: #373b3e;
        }

        .category-section .expandable-content {
            display: none;
            padding: 8px;
            margin-bottom: 8px;
            background-color: #2b3035;
            border: 1px solid #373b3e;
            border-radius: 4px;
        }

        .category-section .expandable-content.show {
            display: block;
        }

        .category-section .nested-section {
            margin-left: 20px;
        }

        .category-section .form-check {
            padding: 8px 12px;
            margin: 0;
        }

        .category-section .form-check:hover {
            background-color: #373b3e;
            border-radius: 4px;
        }

        .category-section .form-check-label {
            color: #fff;
        }

        .category-section .form-check-input {
            background-color: #373b3e;
            border-color: #6c757d;
        }

        .category-section .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .category-section .expand-icon {
            transition: transform 0.2s;
        }

        .category-section .expand-icon.rotated {
            transform: rotate(180deg);
        }

        .category-section .category-count {
            font-size: 0.8em;
            color: #6c757d;
            margin-left: 8px;
        }

        .bold-hr {
            border: 0;
            height: 1px;
            background: #373b3e;
            margin: 1rem 0;
        }
    </style>
</head>

<body>
    <div id="loadingOverlay">
        <div class="earth "></div>
        <span style="color: beige;">Loading...</span>
    </div>

    <section class="container-fluid">
        <div class="row position-relative">
            <div class="col-12 position-relative p-0">
                <div class="position-absolute mt-3 ms-3 z-3">
                    <button class="btn btn-secondary" type="button" data-bs-toggle="offcanvas"
                        data-bs-target="#offcanvasScrolling" aria-controls="offcanvasScrolling">
                        <i class="bi bi-gear-fill h5" style="color: white;"></i>
                    </button>
                </div>
                <div id="cesiumContainer" class="h-100 p-0 m-0"></div>
                <div id="keyboard-controls-info"
                    style="position: absolute; bottom: 10px; left: 10px; background-color: rgba(0,0,0,0.5); color: white; padding: 5px 10px; border-radius: 4px; font-size: 12px; z-index: 1000;">
                    <div><strong>Keyboard Controls:</strong></div>
                    <div>W/↑: Forward, S/↓: Back</div>
                    <div>A/←: Left, D/→: Right</div>
                    <div>Q: Up, E: Down</div>
                    <div>H: Return Home</div>
                    <div>Hold Shift: Speed boost</div>
                </div>
            </div>
        </div>

        <div class="offcanvas offcanvas-start bg-dark" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1"
            id="offcanvasScrolling" aria-labelledby="offcanvasScrollingLabel">

            <div class="offcanvas-header">
                <h5 class="offcanvas-title text-light" id="offcanvasScrollingLabel">
                    configurations
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close">
                </button>
            </div>

            <div class="offcanvas-body position-relative">
                <div class="filter-container" style="padding: 0;">
                    <div class="switch-section">
                        <div class="switch-item">
                            <div class="switch-header">
                                <span>Satellites</span>
                                <div>
                                    <input class="custom-checkbox" type="checkbox" id="isSatellites"
                                        name="classification" />
                                </div>
                            </div>
                        </div>

                        <div class="switch-item">
                            <div class="switch-header">
                                <span>Airplanes</span>
                                <div>
                                    <input class="custom-checkbox" type="checkbox" id="isAirplanes"
                                        name="classification" />
                                </div>
                            </div>
                        </div>

                        <div class="switch-item">
                            <div class="switch-header">
                                <span>Population</span>
                                <div>
                                    <input class="custom-checkbox" type="checkbox" id="isPopulation" checked
                                        name="classification" />
                                </div>
                            </div>
                        </div>

                        <div class="switch-item">
                            <div class="switch-header">
                                <span>Major Roads</span>
                                <div>
                                    <input class="custom-checkbox" type="checkbox" id="isRoads" name="classification" />
                                </div>
                            </div>
                        </div>

                        <!-- i need some placeholder for the filter sections -->
                        <div class="province-filter-section-placeholder"></div>

                        <div class="business-filter-section-placeholder"></div>

                    </div>
                </div>

                <div class="logout-container">
                    <button id="logoutButton" class="btn btn-outline-danger w-100">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        Logout
                    </button>
                </div>
            </div>
        </div>
    </section>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var loadingOverlay = document.getElementById('loadingOverlay');
            loadingOverlay.classList.add('fade-out');

            loadingOverlay.addEventListener('transitionend', function () {
                loadingOverlay.style.display = 'none';
            });
        });
    </script>
</body>

</html>





<!-- <div class="col-6 text-end">
    <div class="form-switch">
        <input class="form-check-input" type="checkbox" role="switch" name="classification"
            id="isSatellites" />
        <label class="form-check-label ms-3" for="isSatellites"></label>
    </div>
</div> -->



<!-- <img
    src="Icons/Airplanes-Flights.svg"
    alt="Airplane Icon"
    width="25"
    height="25"
    class="me-2"
/> -->