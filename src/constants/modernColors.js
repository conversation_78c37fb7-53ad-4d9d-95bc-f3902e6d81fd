import { Color } from "cesium";

export const modernColors = {

    //
    // Sections Colors
    //

    // Current solid colors (commented out)
    // CORAL: Color.fromCssColorString('#FF6B6B'),
    // MUSTARD: Color.fromCssColorString('#FFD93D'),
    // MINT: Color.fromCssColorString('#6BCB77'),
    // SKY_BLUE: Color.fromCssColorString('#4D96FF'),
    // LAVENDER: Color.fromCssColorString('#B088F9'),
    // PEACH: Color.fromCssColorString('#FFB4B4'),
    // LILAC: Color.fromCssColorString('#D7C0AE'),
    // MAUVE: Color.fromCssColorString('#C8B6E2'),
    // SUNSET: Color.fromCssColorString('#FFB26B'),
    // SEAFOAM: Color.fromCssColorString('#98EECC'),

    // Original transparent colors
    CORAL: Color.fromCssColorString('#FF6F61').withAlpha(0.5),
    MUSTARD: Color.fromCssColorString('#FFD166').withAlpha(0.5),
    MINT: Color.fromCssColorString('#06D6A0').withAlpha(0.5),
    SKY_BLUE: Color.fromCssColorString('#72DDF7').withAlpha(0.6),
    LAVENDER: Color.fromCssColorString('#A882DD').withAlpha(0.5),
    PEACH: Color.fromCssColorString('#FF9B85').withAlpha(0.5),
    TEAL: Color.fromCssColorString('#4ECDC4').withAlpha(0.5),
    LILAC: Color.fromCssColorString('#C8A2C8').withAlpha(0.5),
    SALMON: Color.fromCssColorString('#FF8A80').withAlpha(0.5),
    AQUA: Color.fromCssColorString('#80CBC4').withAlpha(0.5),
    MAUVE: Color.fromCssColorString('#B39DDB').withAlpha(0.5),
    SUNSET: Color.fromCssColorString('#FF6B6B').withAlpha(0.5),
    SEAFOAM: Color.fromCssColorString('#77DD77').withAlpha(0.5),
    PERIWINKLE: Color.fromCssColorString('#A3A1F7').withAlpha(0.5),

    //
    // Sections Colors
    //

    // ============================================================================================
    // ============================================================================================
    // ============================================================================================

    //
    // Icons and components colors
    //

    COUNTRYINFOCOLOR: Color.fromCssColorString("#445566"),
    AIRPLANESFLIGHTSCOLOR: Color.fromCssColorString("#486B8C"),
    CLIMATE: Color.fromCssColorString("3E9B77#"),
    CULTUREHERITAGECOLOR: Color.fromCssColorString("#8056A3"),
    ECONOMY: Color.fromCssColorString("#C69214"),
    EDUCATIONCOLOR: Color.fromCssColorString("#4B5DAA"),
    FOOTPRINTCOLOR: Color.fromCssColorString("#755751"),
    HEALTH: Color.fromCssColorString("#D15E4E"),
    INTERNET: Color.fromCssColorString("#1E8B99"),
    POPULATIONCOLOR: Color.fromCssColorString("#CF6D49"),
    RAIN: Color.fromCssColorString("#4A8DB0"),
    SATELLITECOLOR: Color.fromCssColorString("#717D7E"),
    //
    // Icons and components colors
    //


    // ============================================================================================
    // ============================================================================================
    // ============================================================================================


    // Current cluster color (commented out)
    // CLUSTER_ICON_COLOR: Color.fromCssColorString('#2C3333'),

    // Original cluster color
    CLUSTER_ICON_COLOR: Color.fromCssColorString("#EE4E4E"),
};



const countryInfoColor = "#445566";
const airPlanesFlightsColor = "#486B8C";
const climate = "3E9B77#";
const cultureHeritageColor = "#8056A3";
const economy = "#C69214";
const educationColor = "#4B5DAA";
const footprintColor = "#755751";
const health = "#D15E4E";
const internet = "#1E8B99";
const populationColor = "#CF6D49";
const rain = "#4A8DB0";
const satelliteColor = "#717D7E";