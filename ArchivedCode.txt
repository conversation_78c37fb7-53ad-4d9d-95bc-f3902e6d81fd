// Zoom to the plane to track its movement
// viewer.zoomTo(plane);

// // Function to create a plane model
// function addPlaneModel(url, height) {

//     // Define the position and orientation of the plane
//     const position = Cartesian3.fromDegrees(45.4356, 35.5572, height);
//     const heading = CesiumMath.toRadians(135);
//     const pitch = 0;
//     const roll = 0;
//     const hpr = new HeadingPitchRoll(heading, pitch, roll);
//     const orientation = Transforms.headingPitchRollQuaternion(position, hpr);

//     // Add the plane model to the viewer
//     const plane = viewer.entities.add({
//         name: "Airplane",
//         position: position,
//         orientation: orientation,
//         model: {
//             uri: url, // Path to the plane model (local or hosted)
//             minimumPixelSize: 128,
//             maximumScale: 20000,
//         },
//     });

//     // Center the camera on the plane
//     viewer.trackedEntity = plane;
// }


// addPlaneModel("https://raw.githubusercontent.com/CesiumGS/cesium/master/Apps/SampleData/models/CesiumAir/Cesium_Air.glb", 50000.0);











// const issCzml = [
//     {
//         id: "document",
//         name: "ISS Orbit",
//         version: "1.0",
//         clock: {
//             interval: "2025-01-16T00:00:00Z/2025-01-17T00:00:00Z",
//             currentTime: "2025-01-16T00:00:00Z",
//             multiplier: 60,
//         },
//     },
//     {
//         id: "ISS",
//         name: "International Space Station",
//         // position: {
//         //     epoch: "2025-01-16T00:00:00Z",
//         //     cartesian: [
//         //         0, -1716974.0, -1469118.0, 6748940.0,
//         //         60, -1615143.0, -1464375.0, 6745518.0,
//         //         120, -1513248.0, -1459538.0, 6741979.0,
//         //     ],
//         // },
//         position: Cartesian3.fromDegrees(0, 0, 400000),
//         model: {
//             // uri: "./iss.glb",
//             uri: "https://github.com/KhronosGroup/glTF-Sample-Models/blob/main/2.0/MaterialsVariantsShoe/glTF-Binary/MaterialsVariantsShoe.glb",
//             scale: 2000,
//             minimumPixelSize: 30000,
//         },

//         // point: {
//         //     color: { rgba: [255, 0, 0, 255] },
//         //     pixelSize: 10,
//         // },
//     },
// ];

// // Add ISS CZML to viewer
// viewer.dataSources.add(CzmlDataSource.load(issCzml));

// // Fly to ISS
// viewer.scene.camera.flyTo({
//     destination: Cartesian3.fromDegrees(0, 0, 500000),
// });




// const issEntity = viewer.entities.add({
//     name: "International Space Station",
//     availability: new TimeIntervalCollection([
//         new TimeInterval({
//             start: JulianDate.fromIso8601("2025-01-16T00:00:00Z"),
//             stop: JulianDate.fromIso8601("2025-01-16T00:02:00Z"),
//         }),
//     ]),
//     position: new SampledPositionProperty(),
//     model: {
//         uri: "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/ISS/glTF-Binary/ISS.glb",
//         scale: 1,
//     },
// });

// // Add position data
// const position = issEntity.position;
// position.addSample(
//     JulianDate.fromIso8601("2025-01-16T00:00:00Z"),
//     new Cartesian3(-1716974.0, -1469118.0, 6748940.0)
// );
// position.addSample(
//     JulianDate.fromIso8601("2025-01-16T00:01:00Z"),
//     new Cartesian3(-1615143.0, -1464375.0, 6745518.0)
// );
// position.addSample(
//     JulianDate.fromIso8601("2025-01-16T00:02:00Z"),
//     new Cartesian3(-1513248.0, -1459538.0, 6741979.0)
// );

// // Fly camera to initial position
// viewer.scene.camera.flyTo({
//     destination: Cartesian3.fromDegrees(0, 0, 500000),
// });













// Fly the camera to San Francisco at the given longitude, latitude, and height.
// viewer.camera.flyTo({
//     destination: Cartesian3.fromDegrees(45.416107, 35.566864, 100000),
//     // orientation: {
//     //     heading: Math.toRadians(0.0),
//     //     pitch: Math.toRadians(-15.0),
//     // },
// });


// const suliPopulation = { longitude: -75.59777, latitude: 40.03883, height: 400 };


// const pointEntity = viewer.entities.add({
//     description: `First data point at (${suliPopulation.longitude}, ${suliPopulation.latitude})`,
//     position: Cartesian3.fromDegrees(suliPopulation.longitude, suliPopulation.latitude, suliPopulation.height),
//     point: { pixelSize: 100, color: Color.BLUE }
//     // 10 - 100
// });











// WE DOES NOT NEED THE FOCUS FUNCTIONALITY

// Add event listeners to focus buttons
document.getElementById("focusISS").addEventListener("click", () => {
    viewer.trackedEntity = issEntity;
});

document.getElementById("focusPlane").addEventListener("click", () => {

    viewer.trackedEntity = plane;

    // viewer.flyTo(plane, {
    //     duration: 3,
    //     offset: new HeadingPitchRange(
    //         CesiumMath.toRadians(-60),    // Heading: 0 for front view
    //         CesiumMath.toRadians(-13),  // Pitch: -30 degrees for a tilted view
    //         50000                         // Range: 5000 meters away from the entity
    //     ),
    // }).then(function () {
    //     // Once the flyTo animation completes, lock onto the entity

    // });
});





















  // "scripts": {
  //   "test": "echo \"Error: no test specified\" && exit 1",
  //   "build": "node_modules/.bin/webpack --config webpack.config.js",
  //   "start": "node_modules/.bin/webpack serve --config webpack.config.js --open"
  // },






  // GeoJsonDataSource.load("/geo_json/Kurdistan.json", {
//     clampToGround: true,
//     fill: Color.YELLOW.withAlpha(0.30),
// }).then(dataSource => {
//     viewer.dataSources.add(dataSource);

//     const entities = dataSource.entities.values;
//     const distanceDisplayCondition = new DistanceDisplayCondition(400000, 2700000);

//     entities.forEach(entity => {
//         entity.polygon.distanceDisplayCondition = distanceDisplayCondition;
//     });
// }).catch(error => console.log(error));

// const geoJsonUrlSulCity = "/geo_json/.json";

// GeoJsonDataSource.load(geoJsonUrlChamchamalCity, {
//     clampToGround: true,
//     fill: Color.BLANCHEDALMOND.withAlpha(0.45),
// }).then(dataSource => {
//     viewer.dataSources.add(dataSource);

//     const entities = dataSource.entities.values;
//     const distanceDisplayCondition = new DistanceDisplayCondition(10000, 100000000);

//     entities.forEach(entity => {
//         entity.polygon.distanceDisplayCondition = distanceDisplayCondition;
//     });
// }).catch(error => console.log(error));



// Sections("Xanaqin.json", Color.BLUE.withAlpha(0.30));
// Sections("/geo_json/Kifri.json", Color.PURPLE.withAlpha(0.30));
// Sections("/geo_json/Kurdistan.json", Color.YELLOW.withAlpha(0.30));


// Sections("/geo_json/Chamchamal.json", Color.RED.withAlpha(0.30));
// Sections("/geo_json/sharbazher.json", Color.BROWN.withAlpha(0.30));
// Sections("/geo_json/Darband.json", Color.ORANGE.withAlpha(0.30));
// Sections("/geo_json/Dokan.json", Color.GREEN.withAlpha(0.30));
// Sections("/geo_json/Kelar.json", Color.CYAN.withAlpha(0.30));
// Sections("/geo_json/Mawat.json", Color.GOLD.withAlpha(0.30));
// Sections("/geo_json/Sli_city.json", Color.WHITE.withAlpha(0.30));
// Sections("/geo_json/Penjewen.json", Color.GREEN.withAlpha(0.30));
// Sections("/geo_json/Pishdar.json", Color.BROWN.withAlpha(0.30));
// Sections("/geo_json/QaraDagh.json", Color.ORANGE.withAlpha(0.30));
// Sections("/geo_json/Ranya.json", Color.PURPLE.withAlpha(0.30));
// Sections("/geo_json/SayidSadiq.json", Color.BLUE.withAlpha(0.30));
// Sections("/geo_json/Sharazoor.json", Color.LIME.withAlpha(0.30));
// Sections("/geo_json/Bazyan.json", Color.BLUE.withAlpha(0.30));




// const geoJsonPromise = GeoJsonDataSource.load("/geo_json/iraqGeoJson.json", { // city
// const geoJsonPromise = GeoJsonDataSource.load("/geo_json/sli.json", { // province


// const iraqCoordinates = [
//     [38.7923, 37.0660], // Northwest
//     [48.5679, 37.3976], // Northeast
//     [48.4161, 29.9268], // Southeast
//     [38.7923, 29.0990], // Southwest
//     [38.7923, 37.0660]  // Close the polygon
// ].map(coord => Cartesian3.fromDegrees(coord[0], coord[1]));

// // Create the polygon with outline
// viewer.entities.add({
//     name: "Iraq",
//     polygon: {
//         hierarchy: iraqCoordinates,
//         material: Color.TRANSPARENT,
//         outline: true,
//         outlineColor: Color.RED,
//         outlineWidth: 3
//     }
// });






// document.addEventListener("DOMContentLoaded", function () {
//     // Your content is loaded, now fade out the loading overlay
//     var loadingOverlay = document.getElementById('loadingOverlay');
//     loadingOverlay.classList.add('fade-out');

//     // Remove the overlay from the DOM after the transition
//     loadingOverlay.addEventListener('transitionend', function () {
//         loadingOverlay.style.display = 'none';
//     });
// });




// viewer.entities.add({
//     position: Cartesian3.fromDegrees(45.434733, 35.580862),
//     billboard: {
//         image: "Icons/Populations.png", // default: undefined
//         show: true, // default
//         heightReference: HeightReference.CLAMP_TO_GROUND,
//         pixelOffset: new Cartesian2(0, -50), // default: (0, 0)
//         eyeOffset: new Cartesian3(0.0, 0.0, 0.0), // default
//         horizontalOrigin: HorizontalOrigin.CENTER, // default
//         verticalOrigin: VerticalOrigin.BOTTOM, // default: CENTER
//         // scale: 2.0, // default: 1.0
//         color: Color.LIME, // default: WHITE
//         // rotation: CesiumMath.PI_OVER_FOUR, // default: 0.0
//         alignedAxis: Cartesian3.ZERO, // default
//         // width: 100, // default: undefined
//         // height: 25, // default: undefined
//     },
// });





// const filterList = document.createElement("div");
// filterList.innerHTML = `
//   <label>
//     <input type="checkbox" id="toggleISS" checked />
//     Show ISS
//   </label>
//   <br />
//   <label>
//     <input type="checkbox" id="togglePlane" checked />
//     Show Plane
//   </label>
// `;
// filterList.style.position = "absolute";
// filterList.style.top = "10px";
// filterList.style.left = "10px";
// filterList.style.backgroundColor = "rgba(255, 255, 255, 0.8)";
// filterList.style.padding = "10px";
// filterList.style.borderRadius = "8px";
// document.body.appendChild(filterList);


// // Add event listeners to checkboxes
// document.getElementById("toggleISS").addEventListener("change", (event) => {
//     issEntity.show = event.target.checked;
// });

// document.getElementById("togglePlane").addEventListener("change", (event) => {
//     plane.show = event.target.checked;
// });




// // Locate the Cesium toolbar element
// const toolbar = document.querySelector(".cesium-viewer-toolbar");

// // Create a new container for the vertical buttons
// const verticalContainer = document.createElement("div");
// verticalContainer.style.position = "absolute";
// verticalContainer.style.top = "33.33%"; // Adjust as needed
// verticalContainer.style.right = "5px"; // Adjust as needed
// verticalContainer.style.display = "flex";
// verticalContainer.style.flexDirection = "column";
// verticalContainer.style.gap = "10px"; // Add spacing between buttons

// // Append the vertical container to the body or toolbar
// document.body.appendChild(verticalContainer);

// // Create the first new button
// const newButton = document.createElement("button");
// newButton.className = "cesium-button";
// newButton.title = "New Button";

// // Add an icon or text to the button
// newButton.textContent = "Show/Hide AirPlanes"; // Replace with an icon or text as needed

// // Add functionality to the button
// newButton.addEventListener("click", () => {
//     plane.show = !plane.show;
//     if (plane.show === false)
//         viewer.scene.camera.flyTo({
//             destination: Cartesian3.fromDegrees(43.6793, 33.2232, 1500000), // Initial camera view
//         });
// });

// // Append the first new button to the vertical container
// verticalContainer.appendChild(newButton);

// // Create another button to be added below
// const secondButton = document.createElement("button");
// secondButton.className = "cesium-button";
// secondButton.title = "Second Button";

// // Add an icon or text to the second button
// secondButton.textContent = "Show/Hide ISS"; // Replace with an icon or text as needed

// // Add functionality to the second button
// secondButton.addEventListener("click", () => {
//     issEntity.show = !issEntity.show;
//     if (issEntity.show === false)
//         viewer.scene.camera.flyTo({
//             destination: Cartesian3.fromDegrees(43.6793, 33.2232, 1500000), // Initial camera view
//         });
// });

// // Append the second button below the first one
// verticalContainer.appendChild(secondButton);










/*







const response = await fetch("https://6jrxt3td-5657.euw.devtunnels.ms/get-population/all");

const jsons = await response.json();


console.log(jsons);



const populationGroupKey = "population";



const json = [
    { city: "Baghdad", latitude: 33.3153, longitude: 44.3661, Population: 8130000, description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed consequat, neque in egestas scelerisque, lectus enim pulvinar justo, id aliquet diam elit at justo. Maecenas ultricies scelerisque erat, id ullamcorper ipsum placerat nec. Donec rutrum vitae turpis mollis luctus. Aenean pulvinar rutrum porta. In ut libero semper, efficitur odio ac, rhoncus massa. Nunc libero ante, maximus vel tortor non, efficitur dictum ligula. Integer ut neque porta, sagittis lacus in" },
    { city: "Mosul", latitude: 36.3400, longitude: 43.1300, Population: 3730000, description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed consequat, neque in egestas scelerisque, lectus enim pulvinar justo, id aliquet diam elit at justo. Maecenas ultricies scelerisque erat, id ullamcorper ipsum placerat nec. Donec rutrum vitae turpis mollis luctus. Aenean pulvinar rutrum porta. In ut libero semper, efficitur odio ac, rhoncus massa. Nunc libero ante, maximus vel tortor non, efficitur dictum ligula. Integer ut neque porta, sagittis lacus in" },
    { city: "Basra", latitude: 30.5150, longitude: 47.810, Population: 2100000, description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed consequat, neque in egestas scelerisque, lectus enim pulvinar justo, id aliquet diam elit at justo. Maecenas ultricies scelerisque erat, id ullamcorper ipsum placerat nec. Donec rutrum vitae turpis mollis luctus. Aenean pulvinar rutrum porta. In ut libero semper, efficitur odio ac, rhoncus massa. Nunc libero ante, maximus vel tortor non, efficitur dictum ligula. Integer ut neque porta, sagittis lacus in" },
    { city: "Kirkuk", latitude: 35.466, longitude: 44.3167, Population: 1520000, description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed consequat, neque in egestas scelerisque, lectus enim pulvinar justo, id aliquet diam elit at justo. Maecenas ultricies scelerisque erat, id ullamcorper ipsum placerat nec. Donec rutrum vitae turpis mollis luctus. Aenean pulvinar rutrum porta. In ut libero semper, efficitur odio ac, rhoncus massa. Nunc libero ante, maximus vel tortor non, efficitur dictum ligula. Integer ut neque porta, sagittis lacus in" },
    { city: "As Sulaymānīyah", latitude: 35.5572, longitude: 45.4356, Population: 2050000, description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed consequat, neque in egestas scelerisque, lectus enim pulvinar justo, id aliquet diam elit at justo. Maecenas ultricies scelerisque erat, id ullamcorper ipsum placerat nec. Donec rutrum vitae turpis mollis luctus. Aenean pulvinar rutrum porta. In ut libero semper, efficitur odio ac, rhoncus massa. Nunc libero ante, maximus vel tortor non, efficitur dictum ligula. Integer ut neque porta, sagittis lacus in" },
    { city: "Erbil", latitude: 36.1912, longitude: 44.0092, Population: 1850000, description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed consequat, neque in egestas scelerisque, lectus enim pulvinar justo, id aliquet diam elit at justo. Maecenas ultricies scelerisque erat, id ullamcorper ipsum placerat nec. Donec rutrum vitae turpis mollis luctus. Aenean pulvinar rutrum porta. In ut libero semper, efficitur odio ac, rhoncus massa. Nunc libero ante, maximus vel tortor non, efficitur dictum ligula. Integer ut neque porta, sagittis lacus in" },
    { city: "Al Ḩillah", latitude: 32.4775, longitude: 44.4314, Population: 2070000, description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed consequat, neque in egestas scelerisque, lectus enim pulvinar justo, id aliquet diam elit at justo. Maecenas ultricies scelerisque erat, id ullamcorper ipsum placerat nec. Donec rutrum vitae turpis mollis luctus. Aenean pulvinar rutrum porta. In ut libero semper, efficitur odio ac, rhoncus massa. Nunc libero ante, maximus vel tortor non, efficitur dictum ligula. Integer ut neque porta, sagittis lacus in" },
    { city: "Dahūk", latitude: 36.8667, longitude: 43.0000, Population: 1290000, description: "" }
]

const normalizePopulation = (population) => {
    const minPopulation = 1290000;
    const maxPopulation = 8130000;
    const minSize = 20;
    const maxSize = 100;

    return (
        ((population - minPopulation) / (maxPopulation - minPopulation)) *
        (maxSize - minSize) +
        minSize
    );
};

json.forEach((city) => {
    const pixelSize = normalizePopulation(Number(city.Population));

    const sphereRadius = pixelSize / 2;
    const scaleToMeters = 1;
    const heightOffset = sphereRadius * scaleToMeters;

    viewer.entities.add({
        position: Cartesian3.fromDegrees(city.longitude, city.latitude, heightOffset),
        point: {
            // color: Color(0, 0, 255, 150),
            // color: Color.AQUA.withAlpha(0.50),
            // color: Color.fromRgba(0xCF6D49FF),
            color: Color.fromBytes(207, 109, 73, 205),
            // color: Color.fromRandom(),
            // outlineColor: Color.BLUE.withAlpha(0.50),
            outlineColor: Color.fromBytes(165, 87, 58, 255),
            outlineWidth: 2,
            pixelSize: pixelSize,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            // translucencyByDistance: new NearFarScalar(1.5e2, .5, 1.5e7, 0.15),
            // scaleByDistance: new NearFarScalar(1.5e2, .5, 1.5e7, 0.15),
            distanceDisplayCondition: new DistanceDisplayCondition(10.0, 2700000)
        },
        properties: {
            group: populationGroupKey,
        },
        name: "Iraq - " + city.city,
        description: `
        <p>City Name: ${city.city}</p>
        <p>Population: ${city.Population}</p>
        <p>Additional Information:</p>
        <ul>
            <li>${city.description || "No additional data available."}</li>
        </ul>
    `,
    });
});



document.getElementById("isPopulation").addEventListener("change", event => setGroupVisibility(event.target.checked, populationGroupKey));


*/






business pins 

/*
// BUSINESS PINS

const local_businesses = [
    {
        name: "City Center Mall",
        coordinates: { lat: 35.580862, lon: 45.434733 },
        icon: "/icons/mall.svg",
        description: "<p>A premier shopping destination offering a variety of retail stores, dining options, and entertainment facilities.</p>",
    },
    {
        name: "Heritage Bookstore",
        coordinates: { lat: 35.559964, lon: 45.446237 },
        icon: "/icons/bookstore.svg",
        description: "<p>A treasure trove of literature, featuring a vast collection of books across diverse genres and a cozy reading nook.</p>",
    },
    {
        name: "Brew Haven Coffee Shop",
        coordinates: { lat: 35.550102, lon: 45.409843 },
        icon: "/icons/coffee.svg",
        description: "<p>A charming café known for its artisanal coffee blends, freshly baked pastries, and inviting atmosphere.</p>",
    },
    {
        name: "Literary Oasis Bookstore",
        coordinates: { lat: 35.5572, lon: 45.4356 },
        icon: "/icons/bookstore.svg",
        description: "<p>A haven for book enthusiasts, offering an extensive selection of titles and hosting regular literary events.</p>",
    },
    {
        name: "Pages & Co. Bookstore",
        coordinates: { lat: 35.586729, lon: 45.344046 },
        icon: "/icons/bookstore.svg",
        description: "<p>A community-focused bookstore offering a curated selection of books, stationery, and reading accessories.</p>",
    },
];

const businessUrl = "https://6jrxt3td-5657.euw.devtunnels.ms/businesses/businesses+";

viewer.scene.globe.depthTestAgainstTerrain = false;

// Create custom pins using PinBuilder
const pinBuilder = new PinBuilder();

const pin50 = pinBuilder.fromText("50+", Color.RED, 48).toDataURL();
const pin40 = pinBuilder.fromText("40+", Color.ORANGE, 48).toDataURL();
const pin30 = pinBuilder.fromText("30+", Color.YELLOW, 48).toDataURL();
const pin20 = pinBuilder.fromText("20+", Color.GREEN, 48).toDataURL();
const pin10 = pinBuilder.fromText("10+", Color.BLUE, 48).toDataURL();

const defaultPin = pinBuilder.fromUrl("/Icons/business.svg", Color.BLACK, 48);

// Define a group key for businesses
const businessGroupKey = "businesses";

// Create a CustomDataSource for businesses and add it to the viewer
const businessDataSource = new CustomDataSource('businesses');
viewer.dataSources.add(businessDataSource);

// Helper function to create a business entity
function createBusinessEntity(business) {
    return new Entity({
        name: business.name,
        description: business.description,
        position: Cartesian3.fromDegrees(
            business.longitude, // Use API data directly
            business.latitude,  // Use API data directly
            0
        ),
        billboard: {
            image: defaultPin, // Use sub_category for dynamic icons
            verticalOrigin: VerticalOrigin.BOTTOM,
            heightReference: HeightReference.CLAMP_TO_GROUND,
        },
        properties: {
            group: businessGroupKey,
        }
    })
}

function createClusterPin(text, size) {
    return pinBuilder.fromText(text, modernColors.CLUSTER_ICON_COLOR, size).toDataURL();
}

const clusterPins = {
    10000: createClusterPin("10K+", 64),
    2000: createClusterPin("2K+", 64),
    500: createClusterPin("500+", 64),
    100: createClusterPin("100+", 64),
    20: createClusterPin("20+", 48),
    9: createClusterPin("9+", 48),
};

const getClusterIcon = (count) => {
    if (count >= 10000) return clusterPins[10000];
    if (count >= 2000) return clusterPins[2000];
    if (count >= 500) return clusterPins[500];
    if (count >= 100) return clusterPins[100];
    if (count >= 20) return clusterPins[20];
    if (count >= 9) return clusterPins[9];
    return singleDigitPins[count - 2]; // For counts 2-4
};

// Function to fetch business data from API
async function fetchBusinessData() {
    try {
        const response = await fetch(businessUrl);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const apiData = await response.json();

        // Add businesses to data source
        apiData.businesses.forEach(business => {
            businessDataSource.entities.add(createBusinessEntity(business));
        });

        // Initialize visibility
        setGroupVisibility(false, businessGroupKey);

        console.log(`Successfully loaded ${apiData.businesses.length} businesses`);

    } catch (error) {
        console.error('Error fetching business data:', error);
        // You could add user-friendly error handling here (e.g., display a message to the user)
    }
}

// Call the function to load business data
fetchBusinessData();

// Prepare single-digit cluster icons
const singleDigitPins = [];
for (let i = 2; i <= 9; i++) {
    singleDigitPins.push(
        pinBuilder.fromText(`${i}`, Color.BLACK, 48).toDataURL()
    );
}

// Clustering configuration
const clustering = businessDataSource.clustering;
clustering.enabled = true;
clustering.minimumClusterSize = 9;
clustering.pixelRange = 15;

// Cluster event handler
clustering.clusterEvent.addEventListener((clusteredEntities, cluster) => {
    cluster.billboard.heightReference = HeightReference.CLAMP_TO_GROUND;
    cluster.billboard.verticalOrigin = VerticalOrigin.BOTTOM;

    // Hide the cluster label since we'll use a custom billboard
    cluster.label.show = false;

    // Configure the cluster billboard
    cluster.billboard.show = true;
    cluster.billboard.id = cluster.label.id;

    const count = clusteredEntities.length;

    cluster.billboard.image = getClusterIcon(count);

});


*/