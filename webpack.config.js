// The path to the CesiumJS source code
const cesiumSource = "node_modules/cesium/Source";
const cesiumWorkers = "../Build/Cesium/Workers";
const CopyWebpackPlugin = require("copy-webpack-plugin");
const path = require("path");
const webpack = require("webpack");
const HtmlWebpackPlugin = require("html-webpack-plugin");

module.exports = {
    context: __dirname,
    entry: {
        app: "./src/index.js",
    },
    output: {
        filename: "app-" + Date.now() + ".js",
        path: path.resolve(__dirname, "dist"),
        sourcePrefix: "",
        clean: true,
        publicPath: '/',
    },
    amd: {
        toUrlUndefined: true,
    },
    resolve: {
        alias: {
            cesium: path.resolve(__dirname, cesiumSource),
        },
        mainFiles: ["module", "main", "Cesium"],
    },
    module: {
        rules: [
            {
                test: /\.css$/,
                use: ["style-loader", "css-loader"],
            },
            {
                test: /\.(png|gif|jpg|jpeg|svg|xml|json)$/,
                use: ["url-loader"],
            },
        ],
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: "src/index.html",
        }),
        // Copy Cesium Assets, Widgets, and Workers to a static directory
        new CopyWebpackPlugin({
            patterns: [
                { from: path.join(cesiumSource, cesiumWorkers), to: "Workers" },
                { from: path.join(cesiumSource, "Assets"), to: "Assets" },
                { from: path.join(cesiumSource, "Widgets"), to: "Widgets" },
                { from: path.join("./src", "3DModel"), to: "Model" },
                { from: path.join("./src", "Icons"), to: "Icons" },
                { from: path.join("./src", "geo_json"), to: "geo_json" },
                { from: path.join("./public", "login.html"), to: "login.html" },
                { from: path.join("./public", "services"), to: "services" },
                { from: path.join("./public", "js"), to: "js" }
            ],
        }),
        new webpack.DefinePlugin({
            // Define relative base path in cesium for loading assets
            CESIUM_BASE_URL: JSON.stringify("/"),
        }),
    ],
    mode: "development",
    // devtool: 'eval'
    devServer: {

        proxy: [
            {
                context: ['/api'],
                target: 'https://test-digitaltwin.rndlabs.dev',
                secure: true,
                changeOrigin: true,
            },
        ],

        watchFiles: ['src/**/*.html'], // Watch HTML files for changes
        static: {
            directory: path.join(__dirname, 'dist'),
            publicPath: '/',
        },
        hot: true,
        compress: true,
        port: 8080,
        historyApiFallback: true,
    },
};